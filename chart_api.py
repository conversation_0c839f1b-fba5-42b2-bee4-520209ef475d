# chart_api.py
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import psycopg2
from datetime import datetime
from urllib.parse import unquote

app = FastAPI()
app.add_middleware(CORSMiddleware, allow_origins=["*"], allow_methods=["*"], allow_headers=["*"])

DB_CONFIG = {
    "dbname": "eq_DB",
    "user": "postgres",
    "password": "Muni@555",
    "host": "localhost",
    "port": "5432"
}

def fetch_data(symbol, interval_sec):
    conn = psycopg2.connect(**DB_CONFIG)
    cur = conn.cursor()
    cur.execute("""
        SELECT request_time, raw_json
        FROM quote_snapshots
        WHERE symbol = %s
        ORDER BY request_time ASC
        LIMIT 5000
    """, (symbol,))
    rows = cur.fetchall()
    cur.close()
    conn.close()

    result = []
    for ts, data in rows:
        result.append({
            "time": ts.isoformat(),
            "lp": float(data.get("lp", 0)),
            "c": float(data.get("c", 0)),
            "h": float(data.get("h", 0)),
            "l": float(data.get("l", 0)),
            "o": float(data.get("o", 0)),
            "ap": float(data.get("ap", 0)),
            "ltq": int(data.get("ltq", 0)),
            "tbq": int(data.get("tbq", 0)),
            "tsq": int(data.get("tsq", 0)),
            "tbuyq": int(data.get("tbuyq", 0)),
            "tsellq": int(data.get("tsellq", 0))
        })
    return result

@app.get("/chart/{symbol}/{interval_sec}")
def get_chart(symbol: str, interval_sec: int = 5):
    try:
        symbol = unquote(symbol).upper()
        return fetch_data(symbol, interval_sec)
    except Exception as e:
        print(f"❌ Error fetching chart for {symbol}: {e}")
        return {"error": str(e)}