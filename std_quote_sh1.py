import json
from datetime import datetime
from zoneinfo import ZoneInfo
from NorenRestApiPy.NorenApi import NorenApi

# 🔧 Hardcoded input
EXCHANGE = "NSE"
TRADING_SYMBOL = "RELIANCE-EQ"

REST_URL = "https://api.shoonya.com/NorenWClientTP/"
WS_URL   = "wss://api.shoonya.com/NorenWSTP/"

# ✅ Keys to extract from full quote
FILTER_KEYS = [
    "request_time", "stat", "exch", "tsym", "lut", "token", "lp", "c", "h", "l", "ap", "o", "v", "ltq", "ltt",
    "tbq", "tsq",
    "bp1", "sp1", "bp2", "sp2", "bp3", "sp3", "bp4", "sp4", "bp5", "sp5",
    "bq1", "sq1", "bq2", "sq2", "bq3", "sq3", "bq4", "sq4", "bq5", "sq5",
    "bo1", "so1", "bo2", "so2", "bo3", "so3", "bo4", "so4", "bo5", "so5"
]

class ShoonyaSessionLoader:
    def __init__(self, cred_file="Cread.json", token_file="session_token.txt"):
        self.cred_file, self.token_file = cred_file, token_file
        self.api = None

    def load(self):
        try:
            with open(self.cred_file) as cf, open(self.token_file) as tf:
                creds, token = json.load(cf), tf.read().strip()
            self.api = NorenApi(host=REST_URL, websocket=WS_URL)
            self.api.set_session(creds["user_id"], creds["password"], token)
            return True
        except Exception as e:
            print(f"[ShoonyaSessionLoader] Error: {e}")
            return False

def fetch_filtered_quote(exchange: str, symbol: str):
    loader = ShoonyaSessionLoader()
    if not loader.load(): return

    api = loader.api
    query = symbol.upper()
    scrips = api.searchscrip(exchange=exchange, searchtext=query)
    if not scrips or "values" not in scrips:
        print(f"[Search] No match found for {query}")
        return

    token = scrips["values"][0]["token"]
    quote = api.get_quotes(exchange=exchange, token=token)
    if not quote or quote.get("stat") != "Ok":
        print(f"[Quote] Failed to fetch quote for token {token}")
        return

    # ⏰ Add IST timestamp for request_time
    ist_now = datetime.now(ZoneInfo("Asia/Kolkata")).strftime("%H:%M:%S %d-%m-%Y")
    quote["request_time"] = ist_now

    # ⏰ Convert lut (epoch) to IST
    try:
        lut_epoch = int(quote.get("lut", "0"))
        quote["lut"] = datetime.fromtimestamp(lut_epoch, ZoneInfo("Asia/Kolkata")).strftime("%H:%M:%S %d-%m-%Y")
    except:
        quote["lut"] = "Invalid timestamp"

    # 🧹 Filter output
    filtered = {k: quote[k] for k in FILTER_KEYS if k in quote}
    print(json.dumps(filtered, indent=4))

# 🚀 Run with hardcoded values
fetch_filtered_quote(EXCHANGE, TRADING_SYMBOL)