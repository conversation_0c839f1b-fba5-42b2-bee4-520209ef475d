# multi_axis_chart.py - Complete Multi-Axis Market Chart
import warnings
warnings.simplefilter(action='ignore', category=FutureWarning)

import dash
from dash import dcc, html, Input, Output, State
import plotly.graph_objs as go
import pandas as pd
import json
import psycopg2
from datetime import datetime, timedelta
from zoneinfo import ZoneInfo

# --- Database Configuration ---
DB_CONFIG = {
    "dbname": "eq_DB",
    "user": "postgres", 
    "password": "Muni@555",
    "host": "localhost",
    "port": "5432"
}

# --- Constants ---
IST = ZoneInfo("Asia/Kolkata")
SYMBOLS = ["RELIANCE-EQ", "NIFTY INDEX", "INDIA VIX"]
KEYS = ["lp", "c", "h", "l", "o", "ap", "ltq", "tbq", "tsq", "tbuyq", "tsellq"]

# Color scheme for different metrics
METRIC_COLORS = {
    'lp': '#FF4444',    # Red - Last Price
    'c': '#44FF44',     # Green - Close
    'h': '#4444FF',     # Blue - High
    'l': '#FFFF44',     # Yellow - Low
    'o': '#FF44FF',     # Magenta - Open
    'ap': '#44FFFF',    # Cyan - Average Price
    'ltq': '#FF8844',   # Orange - Last Trade Qty
    'tbq': '#8844FF',   # Purple - Total Buy Qty
    'tsq': '#44FF88',   # Light Green - Total Sell Qty
    'tbuyq': '#FF8888', # Light Red - Total Buy Qty
    'tsellq': '#8888FF' # Light Blue - Total Sell Qty
}

# --- Dash App ---
app = dash.Dash(__name__)
app.title = "📈 Multi-Axis Market Chart"

# --- Layout ---
app.layout = html.Div(style={
    'backgroundColor': '#0a0a0a', 
    'color': '#ffffff', 
    'fontFamily': 'Arial',
    'padding': '15px',
    'minHeight': '100vh'
}, children=[
    html.H1("📈 Complete Multi-Axis Market Chart", 
            style={'textAlign': 'center', 'color': '#00ff88', 'marginBottom': '20px'}),
    
    # Controls
    html.Div([
        html.Div([
            html.Label("📅 Select Date:", style={'fontWeight': 'bold', 'color': '#00ff88'}),
            dcc.DatePickerSingle(
                id='date-picker',
                date=datetime.now(IST).date(),
                display_format='DD-MM-YYYY',
                style={'marginLeft': '10px'}
            )
        ], style={'marginRight': '30px'}),
        
        html.Div([
            html.Label("📊 Chart Mode:", style={'fontWeight': 'bold', 'color': '#00ff88'}),
            dcc.RadioItems(
                id='chart-mode',
                options=[
                    {'label': ' 🌟 ALL DATA (Multi-Axis)', 'value': 'multi'},
                    {'label': ' 💰 Price Data Only', 'value': 'price'},
                    {'label': ' 📈 Volume Data Only', 'value': 'volume'},
                    {'label': ' 🔥 Key Metrics Only', 'value': 'key'}
                ],
                value='multi',
                inline=True,
                style={'marginLeft': '10px', 'color': '#ffffff'}
            )
        ], style={'marginRight': '30px'}),

        html.Div([
            html.Label("⚡ Update Mode:", style={'fontWeight': 'bold', 'color': '#00ff88'}),
            dcc.RadioItems(
                id='update-mode',
                options=[
                    {'label': ' 🔄 Auto (30s)', 'value': 'auto'},
                    {'label': ' 📌 Manual Only', 'value': 'manual'}
                ],
                value='auto',
                inline=True,
                style={'marginLeft': '10px', 'color': '#ffffff'}
            )
        ], style={'marginRight': '30px'}),
        
        html.Button("🔄 Refresh Data", id='refresh-btn', 
                   style={'backgroundColor': '#00ff88', 'color': '#000', 'border': 'none',
                          'padding': '12px 25px', 'borderRadius': '8px', 'cursor': 'pointer',
                          'fontWeight': 'bold', 'fontSize': '14px'})
    ], style={'display': 'flex', 'alignItems': 'center', 'justifyContent': 'center', 
              'marginBottom': '25px', 'flexWrap': 'wrap', 'gap': '15px'}),
    
    # Chart
    dcc.Graph(id='multi-axis-chart', style={'height': '800px'}),

    # Line Visibility Controls
    html.Div([
        html.H3("🎛️ Line Visibility Controls", style={'color': '#00ff88', 'textAlign': 'center', 'marginBottom': '15px'}),
        html.Div(id='line-controls', style={'maxHeight': '200px', 'overflowY': 'auto', 'padding': '10px',
                                           'backgroundColor': '#1a1a1a', 'borderRadius': '8px', 'margin': '0 20px'})
    ], style={'marginTop': '20px', 'marginBottom': '20px'}),

    # Status and Legend
    html.Div([
        html.Div(id='chart-status', style={'textAlign': 'center', 'color': '#00ff88', 'fontSize': '16px'}),
        html.Div(id='legend-info', style={'textAlign': 'center', 'color': '#888', 'fontSize': '12px', 'marginTop': '10px'})
    ]),

    # Store for available lines
    dcc.Store(id='available-lines-store'),

    # Auto-refresh
    dcc.Interval(id='auto-refresh', interval=30*1000, n_intervals=0)
])

def fetch_all_data(selected_date):
    """Fetch ALL data for all symbols and metrics"""
    try:
        print(f"🔍 Fetching ALL data for {selected_date}")
        
        conn = psycopg2.connect(**DB_CONFIG)
        cur = conn.cursor()
        
        start_time = datetime.combine(selected_date, datetime.min.time()).replace(tzinfo=IST)
        end_time = start_time + timedelta(days=1)
        
        cur.execute("""
            SELECT symbol, request_time, raw_json
            FROM quote_snapshots
            WHERE request_time >= %s AND request_time < %s
            ORDER BY symbol, request_time
        """, (start_time, end_time))
        
        rows = cur.fetchall()
        cur.close()
        conn.close()
        
        print(f"📊 Found {len(rows)} database records")
        
        if not rows:
            return pd.DataFrame()
        
        # Process ALL data
        data_list = []
        for symbol, request_time, raw_json in rows:
            try:
                if isinstance(raw_json, dict):
                    json_data = raw_json
                else:
                    json_data = json.loads(raw_json)
                
                record = {'symbol': symbol, 'timestamp': request_time}
                for key in KEYS:
                    try:
                        value = json_data.get(key, 0)
                        record[key] = float(value) if value else 0
                    except:
                        record[key] = 0
                
                data_list.append(record)
                
            except Exception as e:
                continue
        
        if not data_list:
            return pd.DataFrame()
        
        df = pd.DataFrame(data_list)
        print(f"✅ Processed {len(df)} records for {df['symbol'].nunique()} symbols")
        return df

    except Exception as e:
        print(f"❌ Database error: {e}")
        return pd.DataFrame()

# Callback to populate line controls based on available data
@app.callback(
    [Output('line-controls', 'children'),
     Output('available-lines-store', 'data')],
    [Input('date-picker', 'date'),
     Input('chart-mode', 'value')]
)
def update_line_controls(selected_date_str, chart_mode):
    try:
        selected_date = datetime.strptime(selected_date_str, '%Y-%m-%d').date()
        df = fetch_all_data(selected_date)

        if df.empty:
            return html.Div("No data available for controls", style={'color': '#888'}), {}

        # Filter keys based on mode
        if chart_mode == 'price':
            active_keys = ['lp', 'c', 'h', 'l', 'o', 'ap']
        elif chart_mode == 'volume':
            active_keys = ['ltq', 'tbq', 'tsq', 'tbuyq', 'tsellq']
        elif chart_mode == 'key':
            active_keys = ['lp', 'ltq', 'tbuyq', 'tsellq']
        else:  # multi
            active_keys = KEYS

        # Build available lines
        available_lines = {}
        controls = []

        for symbol in SYMBOLS:
            symbol_data = df[df['symbol'] == symbol]
            if symbol_data.empty:
                continue

            symbol_lines = []
            for key in active_keys:
                if key in symbol_data.columns and symbol_data[key].sum() != 0:
                    line_id = f"{symbol}_{key}"
                    line_name = f"{symbol} - {key.upper()}"
                    available_lines[line_id] = {
                        'symbol': symbol,
                        'key': key,
                        'name': line_name,
                        'color': METRIC_COLORS.get(key, '#ffffff')
                    }
                    symbol_lines.append(line_id)

            if symbol_lines:
                # Symbol header with master toggle
                controls.append(
                    html.Div([
                        html.Div([
                            html.H5(f"📊 {symbol}", style={
                                'color': METRIC_COLORS.get('lp', '#ffffff'),
                                'margin': '0',
                                'display': 'inline-block'
                            }),
                            html.Button(f"Toggle All {symbol}",
                                       id=f"toggle-{symbol.replace(' ', '-')}",
                                       style={
                                           'marginLeft': '15px',
                                           'backgroundColor': '#333',
                                           'color': '#fff',
                                           'border': '1px solid #555',
                                           'borderRadius': '4px',
                                           'padding': '2px 8px',
                                           'fontSize': '10px',
                                           'cursor': 'pointer'
                                       })
                        ], style={'marginBottom': '8px'}),

                        dcc.Checklist(
                            id=f"lines-{symbol.replace(' ', '-')}",
                            options=[{
                                'label': html.Span([
                                    html.Span("●", style={'color': available_lines[line_id]['color'], 'marginRight': '5px'}),
                                    available_lines[line_id]['name'].split(' - ')[1]
                                ]),
                                'value': line_id
                            } for line_id in symbol_lines],
                            value=symbol_lines,  # All checked by default
                            inline=True,
                            style={'marginLeft': '15px'}
                        )
                    ], style={
                        'marginBottom': '15px',
                        'padding': '8px',
                        'backgroundColor': '#2a2a2a',
                        'borderRadius': '5px',
                        'borderLeft': f'3px solid {METRIC_COLORS.get("lp", "#ffffff")}'
                    })
                )

        return controls, available_lines

    except Exception as e:
        print(f"❌ Error updating line controls: {e}")
        return html.Div(f"Error: {str(e)}", style={'color': '#ff4444'}), {}

@app.callback(
    [Output('multi-axis-chart', 'figure'),
     Output('chart-status', 'children'),
     Output('legend-info', 'children')],
    [Input('refresh-btn', 'n_clicks'),
     Input('auto-refresh', 'n_intervals'),
     Input('date-picker', 'date'),
     Input('chart-mode', 'value'),
     Input('update-mode', 'value'),
     Input('line-controls', 'children')],
    [State('available-lines-store', 'data')]
)
def create_multi_axis_chart(n_clicks, n_intervals, selected_date_str, chart_mode, update_mode,
                           line_controls_children, available_lines_data):
    try:
        print(f"🔄 Creating multi-axis chart - Mode: {chart_mode}, Update: {update_mode}")
        print(f"📊 Selected lines - RELIANCE: {len(reliance_lines or [])}, NIFTY: {len(nifty_lines or [])}, VIX: {len(vix_lines or [])}")

        selected_date = datetime.strptime(selected_date_str, '%Y-%m-%d').date()
        df = fetch_all_data(selected_date)

        if df.empty:
            empty_fig = go.Figure()
            empty_fig.update_layout(
                title=f"📊 No data available for {selected_date.strftime('%d-%m-%Y')}",
                template='plotly_dark',
                height=800,
                paper_bgcolor='#0a0a0a',
                plot_bgcolor='#1a1a1a'
            )
            return empty_fig, f"❌ No data found", ""

        # Get selected lines from all symbols
        selected_lines = []
        if reliance_lines:
            selected_lines.extend(reliance_lines)
        if nifty_lines:
            selected_lines.extend(nifty_lines)
        if vix_lines:
            selected_lines.extend(vix_lines)

        if not selected_lines:
            empty_fig = go.Figure()
            empty_fig.update_layout(
                title="� No lines selected - Use controls below to show data",
                template='plotly_dark',
                height=800,
                paper_bgcolor='#0a0a0a',
                plot_bgcolor='#1a1a1a'
            )
            return empty_fig, "⚠️ No lines selected", "Use the controls below to select which data to display"

        # Filter keys based on mode
        if chart_mode == 'price':
            mode_title = "� PRICE DATA"
        elif chart_mode == 'volume':
            mode_title = "📈 VOLUME DATA"
        elif chart_mode == 'key':
            mode_title = "🔥 KEY METRICS"
        else:  # multi - show everything
            mode_title = "🌟 ALL DATA"
        
        fig = go.Figure()
        axis_count = 1
        total_traces = 0

        # Create traces only for selected lines
        for line_id in selected_lines:
            if not available_lines_data or line_id not in available_lines_data:
                continue

            line_info = available_lines_data[line_id]
            symbol = line_info['symbol']
            key = line_info['key']

            symbol_data = df[df['symbol'] == symbol].copy()
            if symbol_data.empty or key not in symbol_data.columns:
                continue

            # Skip if all values are zero
            if symbol_data[key].sum() == 0:
                continue

            symbol_data = symbol_data.sort_values('timestamp')

            # Assign y-axis
            yaxis_name = 'y' if axis_count == 1 else f'y{axis_count}'

            # Create trace
            fig.add_trace(go.Scatter(
                x=symbol_data['timestamp'],
                y=symbol_data[key],
                mode='lines',
                name=f'{symbol} - {key.upper()}',
                line=dict(
                    color=METRIC_COLORS.get(key, '#ffffff'),
                    width=3 if key == 'lp' else 2
                ),
                yaxis=yaxis_name,
                legendgroup=symbol,
                legendgrouptitle=dict(text=f"📊 {symbol}")
            ))

            # Configure y-axis
            axis_title = f"{symbol} - {key.upper()}"
            axis_color = METRIC_COLORS.get(key, '#ffffff')

            if axis_count == 1:
                fig.update_layout(
                    yaxis=dict(
                        title=dict(text=axis_title, font=dict(color=axis_color, size=10)),
                        side='left',
                        showgrid=True,
                        gridcolor='rgba(128,128,128,0.1)',
                        tickfont=dict(color=axis_color, size=9)
                    )
                )
            else:
                # Alternate sides and positions
                side = 'right' if axis_count % 2 == 0 else 'left'
                if side == 'right':
                    position = 1.0 - ((axis_count // 2 - 1) * 0.08)
                else:
                    position = (axis_count // 2) * 0.08

                position = max(0.02, min(0.98, position))

                fig.update_layout(**{
                    f'yaxis{axis_count}': dict(
                        title=dict(text=axis_title, font=dict(color=axis_color, size=10)),
                        overlaying='y',
                        side=side,
                        position=position,
                        showgrid=False,
                        tickfont=dict(color=axis_color, size=9)
                    )
                })

            axis_count += 1
            total_traces += 1
        
        # Update layout
        fig.update_layout(
            title=dict(
                text=f"📈 {mode_title} - {selected_date.strftime('%d-%m-%Y')} | {total_traces} Series on {axis_count-1} Independent Axes",
                font=dict(size=16, color='#00ff88'),
                x=0.5
            ),
            template='plotly_dark',
            height=800,
            paper_bgcolor='#0a0a0a',
            plot_bgcolor='#1a1a1a',
            xaxis=dict(
                title='Time (IST)',
                showgrid=True,
                gridcolor='rgba(128,128,128,0.1)',
                tickfont=dict(size=10)
            ),
            legend=dict(
                orientation='v',
                yanchor='top',
                y=1,
                xanchor='left',
                x=1.02,
                font=dict(size=9),
                bgcolor='rgba(0,0,0,0.5)'
            ),
            margin=dict(l=100, r=200, t=80, b=60)
        )
        
        # Status messages
        status_msg = f"✅ Displaying {total_traces} data series across {axis_count-1} independent axes"
        legend_msg = f"🎨 Colors: Price data in warm colors, Volume data in cool colors | 📊 Each line has its own Y-axis scale"
        
        return fig, status_msg, legend_msg
        
    except Exception as e:
        print(f"❌ Chart creation error: {e}")
        import traceback
        traceback.print_exc()
        
        error_fig = go.Figure()
        error_fig.update_layout(
            title=f"❌ Error: {str(e)}",
            template='plotly_dark',
            height=800,
            paper_bgcolor='#0a0a0a'
        )
        return error_fig, f"❌ Error: {str(e)}", ""

# Toggle button callbacks for each symbol
@app.callback(
    Output('lines-RELIANCE-EQ', 'value'),
    Input('toggle-RELIANCE-EQ', 'n_clicks'),
    State('lines-RELIANCE-EQ', 'options'),
    prevent_initial_call=True
)
def toggle_reliance_lines(n_clicks, options):
    if n_clicks and n_clicks % 2 == 1:
        return []  # Uncheck all
    else:
        return [opt['value'] for opt in options]  # Check all

@app.callback(
    Output('lines-NIFTY-INDEX', 'value'),
    Input('toggle-NIFTY-INDEX', 'n_clicks'),
    State('lines-NIFTY-INDEX', 'options'),
    prevent_initial_call=True
)
def toggle_nifty_lines(n_clicks, options):
    if n_clicks and n_clicks % 2 == 1:
        return []  # Uncheck all
    else:
        return [opt['value'] for opt in options]  # Check all

@app.callback(
    Output('lines-INDIA-VIX', 'value'),
    Input('toggle-INDIA-VIX', 'n_clicks'),
    State('lines-INDIA-VIX', 'options'),
    prevent_initial_call=True
)
def toggle_vix_lines(n_clicks, options):
    if n_clicks and n_clicks % 2 == 1:
        return []  # Uncheck all
    else:
        return [opt['value'] for opt in options]  # Check all

if __name__ == '__main__':
    print("🚀 Starting Multi-Axis Market Chart...")
    print("📊 This will show ALL data with independent axes")
    print("🌐 Open http://localhost:8052 in your browser")
    app.run_server(debug=True, host='0.0.0.0', port=8052)
