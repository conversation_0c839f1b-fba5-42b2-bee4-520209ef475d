# market_chart_dash.py - Interactive Market Chart using Dash
import warnings
warnings.simplefilter(action='ignore', category=FutureWarning)

import dash
from dash import dcc, html, callback_context
from dash.dependencies import Input, Output, State
import plotly.graph_objs as go
import pandas as pd
import json
from sqlalchemy import create_engine
from datetime import datetime, timedelta
import eq_cred as cr
from config import IST

# --- Database Connection ---
db_url = f"postgresql://{cr.DB_USER}:{cr.DB_PASS_ENCODED}@{cr.DB_HOST}:{cr.DB_PORT}/{cr.DB_NAME}"
engine = create_engine(db_url)

# --- Constants ---
SYMBOLS = ["RELIANCE-EQ", "NIFTY INDEX", "INDIA VIX"]
KEYS = ["lp", "c", "h", "l", "o", "ap", "ltq", "tbq", "tsq", "tbuyq", "tsellq"]
COLORS = ["#FF6B6B", "#4ECDC4", "#45B7D1", "#96CEB4", "#FFEAA7", "#DDA0DD", "#98D8C8", "#F7DC6F", "#BB8FCE", "#85C1E9", "#F8C471"]

# --- Dash App ---
app = dash.Dash(__name__, update_title='Market Chart')
app.title = "📈 Market Chart Viewer"

# --- Layout ---
app.layout = html.Div(style={
    'backgroundColor': '#1e1e1e', 
    'color': '#ffffff', 
    'fontFamily': 'Arial, sans-serif',
    'minHeight': '100vh',
    'padding': '20px'
}, children=[
    html.H1("📈 Market Chart Viewer", style={
        'textAlign': 'center', 
        'marginBottom': '30px',
        'color': '#4ECDC4'
    }),
    
    # Controls Row
    html.Div([
        html.Div([
            html.Label("📅 Date:", style={'fontWeight': 'bold', 'marginBottom': '5px'}),
            dcc.DatePickerSingle(
                id='date-picker',
                date=datetime.now(IST).date(),
                display_format='DD-MM-YYYY',
                style={'width': '150px'}
            )
        ], style={'marginRight': '20px'}),
        
        html.Div([
            html.Label("⏱️ Interval:", style={'fontWeight': 'bold', 'marginBottom': '5px'}),
            dcc.Dropdown(
                id='interval-selector',
                options=[
                    {'label': '5 seconds', 'value': 5},
                    {'label': '30 seconds', 'value': 30},
                    {'label': '1 minute', 'value': 60},
                    {'label': '5 minutes', 'value': 300},
                    {'label': '15 minutes', 'value': 900},
                    {'label': '1 hour', 'value': 3600},
                ],
                value=5,
                clearable=False,
                style={'width': '150px', 'color': '#000'}
            )
        ], style={'marginRight': '20px'}),
        
        html.Button("🔄 Refresh", id='refresh-btn', 
                   style={
                       'backgroundColor': '#4ECDC4', 
                       'color': 'white', 
                       'border': 'none',
                       'padding': '10px 20px',
                       'borderRadius': '5px',
                       'cursor': 'pointer',
                       'fontSize': '14px'
                   })
    ], style={
        'display': 'flex', 
        'alignItems': 'end', 
        'justifyContent': 'center', 
        'marginBottom': '30px',
        'flexWrap': 'wrap',
        'gap': '10px'
    }),
    
    # Symbol Selection
    html.Div(id='symbol-checkboxes', style={'marginBottom': '20px'}),
    
    # Chart
    dcc.Graph(
        id='market-chart',
        style={'height': '700px'},
        config={
            'displayModeBar': True,
            'scrollZoom': True,
            'displaylogo': False
        }
    ),
    
    # Auto-refresh interval
    dcc.Interval(
        id='auto-refresh',
        interval=30*1000,  # 30 seconds
        n_intervals=0
    ),
    
    # Data store
    dcc.Store(id='chart-data-store')
])

def create_symbol_checkboxes():
    """Create organized checkbox layout for symbols and their keys"""
    children = []
    
    for symbol in SYMBOLS:
        # Symbol header
        symbol_header = html.Div([
            html.H4(f"📊 {symbol}", style={
                'margin': '10px 0 5px 0',
                'padding': '8px 12px',
                'backgroundColor': '#2d2d2d',
                'borderRadius': '5px',
                'borderLeft': f'4px solid {COLORS[SYMBOLS.index(symbol)]}'
            }),
            
            # Master checkbox for symbol
            html.Div([
                dcc.Checklist(
                    id=f'{symbol}-master',
                    options=[{'label': f' Select All {symbol}', 'value': 'all'}],
                    value=['all'] if symbol == 'RELIANCE-EQ' else [],
                    inline=True,
                    style={'marginBottom': '10px'}
                )
            ]),
            
            # Individual key checkboxes
            html.Div([
                dcc.Checklist(
                    id=f'{symbol}-keys',
                    options=[{'label': f' {key.upper()}', 'value': key} for key in KEYS],
                    value=['lp'] if symbol == 'RELIANCE-EQ' else [],
                    inline=True,
                    style={
                        'marginLeft': '20px',
                        'display': 'grid',
                        'gridTemplateColumns': 'repeat(auto-fit, minmax(100px, 1fr))',
                        'gap': '5px'
                    }
                )
            ])
        ], style={
            'marginBottom': '20px',
            'padding': '10px',
            'backgroundColor': '#2a2a2a',
            'borderRadius': '8px'
        })
        
        children.append(symbol_header)
    
    return html.Div(children)

# Callback to populate symbol checkboxes
@app.callback(
    Output('symbol-checkboxes', 'children'),
    Input('date-picker', 'date')
)
def update_symbol_checkboxes(selected_date):
    return create_symbol_checkboxes()

# Master checkbox callbacks for each symbol
@app.callback(
    Output('RELIANCE-EQ-keys', 'value'),
    Input('RELIANCE-EQ-master', 'value'),
    State('RELIANCE-EQ-keys', 'value'),
    prevent_initial_call=True
)
def toggle_reliance_keys(master_value, current_keys):
    if 'all' in (master_value or []):
        return KEYS
    else:
        return []

@app.callback(
    Output('NIFTY INDEX-keys', 'value'),
    Input('NIFTY INDEX-master', 'value'),
    State('NIFTY INDEX-keys', 'value'),
    prevent_initial_call=True
)
def toggle_nifty_keys(master_value, current_keys):
    if 'all' in (master_value or []):
        return KEYS
    else:
        return []

@app.callback(
    Output('INDIA VIX-keys', 'value'),
    Input('INDIA VIX-master', 'value'),
    State('INDIA VIX-keys', 'value'),
    prevent_initial_call=True
)
def toggle_vix_keys(master_value, current_keys):
    if 'all' in (master_value or []):
        return KEYS
    else:
        return []

def fetch_market_data(selected_date, interval_seconds=5):
    """Fetch data from quote_snapshots table"""
    try:
        # Convert date to datetime range
        start_time = datetime.combine(selected_date, datetime.min.time()).replace(tzinfo=IST)
        end_time = start_time + timedelta(days=1)

        query = """
            SELECT symbol, request_time, raw_json
            FROM quote_snapshots
            WHERE request_time >= %(start_time)s AND request_time < %(end_time)s
            ORDER BY symbol, request_time
        """

        df = pd.read_sql(query, engine, params={
            'start_time': start_time,
            'end_time': end_time
        })

        if df.empty:
            return pd.DataFrame()

        # Parse JSON data and expand columns
        expanded_data = []
        for _, row in df.iterrows():
            try:
                json_data = row['raw_json'] if isinstance(row['raw_json'], dict) else json.loads(row['raw_json'])
                record = {
                    'symbol': row['symbol'],
                    'timestamp': row['request_time'],
                    **{key: json_data.get(key, 0) for key in KEYS}
                }
                expanded_data.append(record)
            except Exception as parse_error:
                print(f"Error parsing JSON for {row['symbol']}: {parse_error}")
                continue

        if not expanded_data:
            return pd.DataFrame()

        result_df = pd.DataFrame(expanded_data)

        # Convert numeric columns
        for key in KEYS:
            if key in result_df.columns:
                result_df[key] = pd.to_numeric(result_df[key], errors='coerce').fillna(0)

        # Ensure timestamp is timezone-aware
        result_df['timestamp'] = pd.to_datetime(result_df['timestamp'])
        if result_df['timestamp'].dt.tz is None:
            result_df['timestamp'] = result_df['timestamp'].dt.tz_localize(IST)
        else:
            result_df['timestamp'] = result_df['timestamp'].dt.tz_convert(IST)

        return result_df

    except Exception as e:
        print(f"Error fetching data: {e}")
        import traceback
        traceback.print_exc()
        return pd.DataFrame()

# Main chart callback
@app.callback(
    [Output('market-chart', 'figure'),
     Output('chart-data-store', 'data')],
    [Input('refresh-btn', 'n_clicks'),
     Input('auto-refresh', 'n_intervals'),
     Input('date-picker', 'date'),
     Input('interval-selector', 'value')] +
    [Input(f'{symbol}-keys', 'value') for symbol in SYMBOLS],
    prevent_initial_call=False
)
def update_chart(refresh_clicks, auto_refresh, selected_date_str, interval_sec, *symbol_selections):
    try:
        print(f"🔄 Updating chart for date: {selected_date_str}, interval: {interval_sec}")
        print(f"📊 Symbol selections: {symbol_selections}")

        selected_date = datetime.strptime(selected_date_str, '%Y-%m-%d').date()

        # Fetch data
        df = fetch_market_data(selected_date, interval_sec)
        print(f"📈 Fetched {len(df)} rows of data")
        
        if df.empty:
            empty_fig = go.Figure()
            empty_fig.update_layout(
                title=f"📊 No data available for {selected_date.strftime('%d-%m-%Y')}",
                template='plotly_dark',
                height=700,
                font=dict(color='white')
            )
            return empty_fig, df.to_json(orient='split', date_format='iso')
        
        # Create figure
        fig = go.Figure()
        
        # Track axis assignments
        axis_count = 1
        
        # Process each symbol
        for i, symbol in enumerate(SYMBOLS):
            selected_keys = symbol_selections[i] or []
            if not selected_keys:
                continue
                
            symbol_data = df[df['symbol'] == symbol].copy()
            if symbol_data.empty:
                continue
            
            # Sort by timestamp
            symbol_data = symbol_data.sort_values('timestamp')
            
            # Assign y-axis for this symbol
            yaxis_name = 'y' if axis_count == 1 else f'y{axis_count}'
            symbol_color = COLORS[i % len(COLORS)]
            
            # Add traces for selected keys
            for j, key in enumerate(selected_keys):
                if key in symbol_data.columns:
                    # Different line styles for different keys
                    line_dash = ['solid', 'dash', 'dot', 'dashdot'][j % 4]
                    
                    fig.add_trace(go.Scatter(
                        x=symbol_data['timestamp'],
                        y=symbol_data[key],
                        mode='lines+markers',
                        name=f'{key.upper()}',
                        line=dict(
                            color=symbol_color,
                            dash=line_dash,
                            width=2 if key == 'lp' else 1.5
                        ),
                        marker=dict(size=3),
                        yaxis=yaxis_name,
                        legendgroup=symbol,
                        legendgrouptitle=dict(text=symbol)
                    ))
            
            # Configure y-axis for this symbol
            if axis_count == 1:
                fig.update_layout(
                    yaxis=dict(
                        title=dict(text=symbol, font=dict(color=symbol_color)),
                        side='left',
                        showgrid=True,
                        gridcolor='rgba(128,128,128,0.2)'
                    )
                )
            else:
                side = 'right' if axis_count % 2 == 0 else 'left'
                position = 1.0 - (axis_count // 2) * 0.1 if side == 'right' else (axis_count // 2) * 0.1
                
                fig.update_layout(**{
                    f'yaxis{axis_count}': dict(
                        title=dict(text=symbol, font=dict(color=symbol_color)),
                        overlaying='y',
                        side=side,
                        position=max(0, min(1, position)),
                        showgrid=False
                    )
                })
            
            axis_count += 1
        
        # Update layout
        fig.update_layout(
            title=f"📊 Market Data - {selected_date.strftime('%d-%m-%Y')} (Interval: {interval_sec}s)",
            template='plotly_dark',
            height=700,
            xaxis=dict(
                title='Time (IST)',
                showgrid=True,
                gridcolor='rgba(128,128,128,0.2)'
            ),
            legend=dict(
                orientation='h',
                yanchor='bottom',
                y=1.02,
                xanchor='right',
                x=1
            ),
            margin=dict(l=80, r=80, t=100, b=50),
            font=dict(color='white')
        )
        
        return fig, df.to_json(orient='split', date_format='iso')
        
    except Exception as e:
        print(f"Error updating chart: {e}")
        error_fig = go.Figure()
        error_fig.update_layout(
            title=f"❌ Error loading chart: {str(e)}",
            template='plotly_dark',
            height=700,
            font=dict(color='white')
        )
        return error_fig, pd.DataFrame().to_json(orient='split')

if __name__ == '__main__':
    print("🚀 Starting Market Chart Dashboard...")
    print("📊 Open http://localhost:8050 in your browser")
    app.run_server(debug=True, host='0.0.0.0', port=8050)
