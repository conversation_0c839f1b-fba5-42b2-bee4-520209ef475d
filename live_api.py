<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>📊 Market Snapshot Dashboard</title>
  <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
  <style>
    body { font-family: Arial, sans-serif; margin: 20px; background: #f9f9f9; }
    h2 { margin-bottom: 10px; }
    .chart-container { margin-bottom: 50px; background: #fff; padding: 20px; border-radius: 8px; box-shadow: 0 0 10px rgba(0,0,0,0.1); }
    .checkbox-group { margin-bottom: 20px; }
    label { font-weight: bold; }
    .status { font-size: 14px; margin-bottom: 10px; color: #555; }
  </style>
</head>
<body>

<h2>📈 Live Market Snapshot</h2>

<div class="checkbox-group">
  <label><input type="checkbox" id="toggleAxes" checked> Show All Y-Axes</label>
</div>
<div class="status" id="status">🔄 Fetching live data…</div>

<div class="chart-container" id="chart-reliance"></div>
<div class="chart-container" id="chart-nifty"></div>
<div class="chart-container" id="chart-vix"></div>

<script>
let lastKnownData = {
  "RELIANCE-EQ": {},
  "NIFTY INDEX": {},
  "INDIA VIX": {}
};

async function fetchLiveData() {
  try {
    const res = await fetch("http://localhost:8000/latest");
    if (!res.ok) throw new Error("API error");
    const data = await res.json();
    document.getElementById("status").textContent = "✅ Live data updated at " + new Date().toLocaleTimeString();
    lastKnownData = data;
    return {
      relianceData: { time: new Date().toLocaleTimeString(), values: data["RELIANCE-EQ"] || {} },
      niftyData: { time: new Date().toLocaleTimeString(), values: data["NIFTY INDEX"] || {} },
      vixData: { time: new Date().toLocaleTimeString(), values: data["INDIA VIX"] || {} }
    };
  } catch (err) {
    console.warn("⚠️ Live fetch failed, using last known data.");
    document.getElementById("status").textContent = "⚠️ Live data unavailable. Showing last known snapshot.";
    return {
      relianceData: { time: new Date().toLocaleTimeString(), values: lastKnownData["RELIANCE-EQ"] || {} },
      niftyData: { time: new Date().toLocaleTimeString(), values: lastKnownData["NIFTY INDEX"] || {} },
      vixData: { time: new Date().toLocaleTimeString(), values: lastKnownData["INDIA VIX"] || {} }
    };
  }
}

function createChart(containerId, symbol, data, axesVisible) {
  const keys = Object.keys(data.values);
  if (keys.length === 0) {
    document.getElementById(containerId).innerHTML = `<p style="color:red;">⚠️ No data available for ${symbol}</p>`;
    return;
  }

  const traces = keys.map((key, i) => ({
    x: [data.time],
    y: [parseFloat(data.values[key])],
    name: key,
    yaxis: i === 0 ? 'y' : `y${i+1}`,
    type: 'scatter',
    mode: 'lines+markers'
  }));

  const layout = {
    title: symbol,
    xaxis: { title: 'Time' },
    showlegend: true,
    margin: { t: 40 },
  };

  keys.forEach((key, i) => {
    layout[`yaxis${i === 0 ? '' : i+1}`] = {
      title: key,
      overlaying: 'y',
      side: i % 2 === 0 ? 'left' : 'right',
      visible: axesVisible,
      showgrid: false
    };
  });

  Plotly.newPlot(containerId, traces, layout);
}

async function renderAllCharts(showAxes) {
  const { relianceData, niftyData, vixData } = await fetchLiveData();
  createChart('chart-reliance', 'RELIANCE-EQ', relianceData, showAxes);
  createChart('chart-nifty', 'NIFTY INDEX', niftyData, showAxes);
  createChart('chart-vix', 'INDIA VIX', vixData, showAxes);
}

document.getElementById('toggleAxes').addEventListener('change', function() {
  renderAllCharts(this.checked);
});

setInterval(() => renderAllCharts(document.getElementById('toggleAxes').checked), 5000);
renderAllCharts(true);
</script>

</body>
</html>