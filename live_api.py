# live_api.py - Live Market Data API Server
from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse
import psycopg2
import json
from datetime import datetime, timedelta
from zoneinfo import ZoneInfo
import uvicorn

# --- Database Configuration ---
DB_CONFIG = {
    "dbname": "eq_DB",
    "user": "postgres",
    "password": "Muni@555",
    "host": "localhost",
    "port": "5432"
}

# --- Constants ---
IST = ZoneInfo("Asia/Kolkata")
SYMBOLS = ["RELIANCE-EQ", "NIFTY INDEX", "INDIA VIX"]

# --- FastAPI App ---
app = FastAPI(title="📊 Live Market Data API", version="1.0.0")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

def get_latest_data():
    """Fetch the latest data for all symbols from database"""
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        cur = conn.cursor()

        result = {}
        for symbol in SYMBOLS:
            # Get the most recent record for this symbol
            cur.execute("""
                SELECT raw_json, request_time
                FROM quote_snapshots
                WHERE symbol = %s
                ORDER BY request_time DESC
                LIMIT 1
            """, (symbol,))

            row = cur.fetchone()
            if row:
                raw_json, request_time = row
                if isinstance(raw_json, dict):
                    data = raw_json
                else:
                    data = json.loads(raw_json)

                # Add timestamp info
                data['last_updated'] = request_time.strftime('%H:%M:%S')
                result[symbol] = data
            else:
                result[symbol] = {}

        cur.close()
        conn.close()
        return result

    except Exception as e:
        print(f"❌ Database error: {e}")
        return {}

def get_historical_data(symbol, hours=1):
    """Fetch historical data for a symbol"""
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        cur = conn.cursor()

        # Get data from last N hours
        start_time = datetime.now(IST) - timedelta(hours=hours)

        cur.execute("""
            SELECT raw_json, request_time
            FROM quote_snapshots
            WHERE symbol = %s AND request_time >= %s
            ORDER BY request_time ASC
        """, (symbol, start_time))

        rows = cur.fetchall()
        result = []

        for raw_json, request_time in rows:
            if isinstance(raw_json, dict):
                data = raw_json
            else:
                data = json.loads(raw_json)

            # Add timestamp
            data['timestamp'] = request_time.isoformat()
            result.append(data)

        cur.close()
        conn.close()
        return result

    except Exception as e:
        print(f"❌ Database error: {e}")
        return []

@app.get("/")
async def root():
    """API Information"""
    return {
        "message": "📊 Live Market Data API",
        "version": "1.0.0",
        "endpoints": {
            "/latest": "Get latest data for all symbols",
            "/symbol/{symbol}": "Get latest data for specific symbol",
            "/history/{symbol}": "Get historical data for symbol",
            "/dashboard": "Live market dashboard (HTML)"
        }
    }

@app.get("/latest")
async def get_latest():
    """Get latest data for all symbols"""
    data = get_latest_data()
    if not data:
        raise HTTPException(status_code=404, detail="No data available")

    return {
        "status": "success",
        "timestamp": datetime.now(IST).isoformat(),
        "data": data
    }

@app.get("/symbol/{symbol}")
async def get_symbol_data(symbol: str):
    """Get latest data for a specific symbol"""
    symbol = symbol.upper()
    if symbol not in SYMBOLS:
        raise HTTPException(status_code=404, detail=f"Symbol {symbol} not found")

    data = get_latest_data()
    if symbol not in data or not data[symbol]:
        raise HTTPException(status_code=404, detail=f"No data available for {symbol}")

    return {
        "status": "success",
        "symbol": symbol,
        "timestamp": datetime.now(IST).isoformat(),
        "data": data[symbol]
    }

@app.get("/history/{symbol}")
async def get_symbol_history(symbol: str, hours: int = 1):
    """Get historical data for a symbol"""
    symbol = symbol.upper()
    if symbol not in SYMBOLS:
        raise HTTPException(status_code=404, detail=f"Symbol {symbol} not found")

    if hours < 1 or hours > 24:
        raise HTTPException(status_code=400, detail="Hours must be between 1 and 24")

    data = get_historical_data(symbol, hours)
    if not data:
        raise HTTPException(status_code=404, detail=f"No historical data available for {symbol}")

    return {
        "status": "success",
        "symbol": symbol,
        "hours": hours,
        "records": len(data),
        "data": data
    }

@app.get("/dashboard", response_class=HTMLResponse)
async def get_dashboard():
    """Live market dashboard"""
    html_content = """
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>📊 Live Market Dashboard</title>
        <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; background: #0a0a0a; color: #fff; }
            .header { text-align: center; margin-bottom: 30px; }
            .status { text-align: center; margin-bottom: 20px; padding: 10px; background: #1a1a1a; border-radius: 5px; }
            .chart-container { margin-bottom: 30px; padding: 20px; background: #1a1a1a; border-radius: 8px; }
            .controls { text-align: center; margin-bottom: 20px; }
            .controls button { margin: 0 10px; padding: 10px 20px; background: #00ff88; color: #000; border: none; border-radius: 5px; cursor: pointer; }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>� Live Market Dashboard</h1>
        </div>

        <div class="controls">
            <button onclick="toggleAutoRefresh()">🔄 Toggle Auto Refresh</button>
            <button onclick="refreshData()">📊 Refresh Now</button>
        </div>

        <div class="status" id="status">🔄 Loading...</div>

        <div class="chart-container" id="chart-reliance"></div>
        <div class="chart-container" id="chart-nifty"></div>
        <div class="chart-container" id="chart-vix"></div>

        <script>
            let autoRefresh = true;
            let refreshInterval;

            async function fetchData() {
                try {
                    const response = await fetch('/latest');
                    const result = await response.json();
                    document.getElementById('status').innerHTML = `✅ Last updated: ${new Date().toLocaleTimeString()}`;
                    return result.data;
                } catch (error) {
                    document.getElementById('status').innerHTML = `❌ Error: ${error.message}`;
                    return null;
                }
            }

            function createChart(containerId, symbol, data) {
                if (!data || Object.keys(data).length === 0) {
                    document.getElementById(containerId).innerHTML = `<h3>${symbol}</h3><p>No data available</p>`;
                    return;
                }

                const keys = Object.keys(data).filter(key => key !== 'last_updated');
                const traces = keys.map(key => ({
                    x: [data.last_updated || 'Now'],
                    y: [parseFloat(data[key]) || 0],
                    name: key.toUpperCase(),
                    type: 'bar'
                }));

                const layout = {
                    title: `📊 ${symbol}`,
                    template: 'plotly_dark',
                    showlegend: true,
                    height: 400
                };

                Plotly.newPlot(containerId, traces, layout);
            }

            async function refreshData() {
                const data = await fetchData();
                if (data) {
                    createChart('chart-reliance', 'RELIANCE-EQ', data['RELIANCE-EQ']);
                    createChart('chart-nifty', 'NIFTY INDEX', data['NIFTY INDEX']);
                    createChart('chart-vix', 'INDIA VIX', data['INDIA VIX']);
                }
            }

            function toggleAutoRefresh() {
                autoRefresh = !autoRefresh;
                if (autoRefresh) {
                    refreshInterval = setInterval(refreshData, 5000);
                    document.getElementById('status').innerHTML += ' | 🔄 Auto-refresh ON';
                } else {
                    clearInterval(refreshInterval);
                    document.getElementById('status').innerHTML += ' | ⏸️ Auto-refresh OFF';
                }
            }

            // Initialize
            refreshData();
            refreshInterval = setInterval(refreshData, 5000);
        </script>
    </body>
    </html>
    """
    return html_content

if __name__ == "__main__":
    print("🚀 Starting Live Market Data API...")
    print("📊 API will be available at:")
    print("   - http://localhost:8000 (API info)")
    print("   - http://localhost:8000/latest (Latest data)")
    print("   - http://localhost:8000/dashboard (Live dashboard)")
    uvicorn.run(app, host="0.0.0.0", port=8000, reload=True)