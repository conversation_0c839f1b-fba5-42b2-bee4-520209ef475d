import json
from NorenRestApiPy.NorenApi import NorenApi

# 🔧 Hardcoded input: exchange and trading symbol
EXCHANGE = "NSE"
TRADING_SYMBOL = "RELIANCE-EQ"

REST_URL = "https://api.shoonya.com/NorenWClientTP/"
WS_URL   = "wss://api.shoonya.com/NorenWSTP/"

class ShoonyaSessionLoader:
    def __init__(self, cred_file="Cread.json", token_file="session_token.txt"):
        self.cred_file, self.token_file = cred_file, token_file
        self.api = None

    def load(self):
        try:
            with open(self.cred_file) as cf, open(self.token_file) as tf:
                creds, token = json.load(cf), tf.read().strip()
            self.api = NorenApi(host=REST_URL, websocket=WS_URL)
            self.api.set_session(creds["user_id"], creds["password"], token)
            return True
        except Exception as e:
            print(f"[ShoonyaSessionLoader] Error: {e}")
            return False

def fetch_full_quote(exchange: str, symbol: str):
    loader = ShoonyaSessionLoader()
    if not loader.load(): return

    api = loader.api
    query = symbol.upper()
    scrips = api.searchscrip(exchange=exchange, searchtext=query)
    if not scrips or "values" not in scrips:
        print(f"[Search] No match found for {query}")
        return

    token = scrips["values"][0]["token"]
    quote = api.get_quotes(exchange=exchange, token=token)
    if not quote or quote.get("stat") != "Ok":
        print(f"[Quote] Failed to fetch quote for token {token}")
        return

    print(json.dumps(quote, indent=4))  # ✅ Full dictionary output

# 🚀 Run with hardcoded values
fetch_full_quote(EXCHANGE, TRADING_SYMBOL)