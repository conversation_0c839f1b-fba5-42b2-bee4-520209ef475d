# login_adv1_sh1.py
from __future__ import annotations
import json, os
from datetime import datetime
from typing import Optional
from pyotp import TOTP
from NorenRestApiPy.NorenApi import NorenApi

class ShoonyaClient:
    REST_URL, WS_URL = "https://api.shoonya.com/NorenWClientTP/", "wss://api.shoonya.com/NorenWSTP/"

    def __init__(self, cred_file="Cread.json", token_file="session_token.txt"):
        self.cred_file, self.token_file = cred_file, token_file
        self._api, self._creds = None, None

    def login(self) -> bool:
        try:
            with open(self.cred_file) as f: self._creds = json.load(f)
            self._api = NorenApi(host=self.REST_URL, websocket=self.WS_URL)
            otp = TOTP(self._creds["totp"]).now().zfill(6)
            r = self._api.login(userid=self._creds["user_id"], password=self._creds["password"],
                                twoFA=otp, vendor_code=self._creds["vendor_code"],
                                api_secret=self._creds["app_key"], imei=self._creds["imei"])
            if not r or "susertoken" not in r: raise ValueError(r)
            with open(self.token_file, "w") as f: f.write(r["susertoken"])
            print(f"[{datetime.now():%H:%M:%S}] Login successful")
            return True
        except Exception as e:
            print(f"[{datetime.now():%H:%M:%S}] Login failed -> {e}")
            return False

    @property
    def api(self) -> Optional[NorenApi]: return self._api

if __name__ == "__main__":
    c = ShoonyaClient()
    if c.login(): api = c.api  # api.place_order(...) etc.