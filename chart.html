<!DOCTYPE html>
<html>
<head>
  <title>Full Market Chart Viewer</title>
  <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
  <style>
    body { font-family: Arial; margin: 20px; }
    .checkbox-group { display: grid; grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); gap: 5px; margin-bottom: 10px; }
    .group-label { font-weight: bold; margin-top: 10px; }
  </style>
</head>
<body>
  <h2>📈 Full Market Chart Viewer</h2>
  <label>Interval:
    <select id="interval">
      <option value="5">5s</option>
      <option value="30">30s</option>
      <option value="60">1m</option>
      <option value="300">5m</option>
      <option value="900">15m</option>
      <option value="4500">75m</option>
      <option value="86400">Daily</option>
    </select>
  </label>
  <button onclick="renderChart()">🔄 Refresh</button>

  <div class="checkbox-group" id="checkboxes"></div>
  <div id="chart" style="height: 700px;"></div>

  <script>
    const symbols = ["RELIANCE-EQ", "NIFTY INDEX", "INDIA VIX"];
    const keys = ["lp", "c", "h", "l", "o", "ap", "ltq", "tbq", "tsq", "tbuyq", "tsellq"];
    const colors = ["red", "blue", "green", "orange", "purple", "teal", "brown", "gray", "pink", "gold", "cyan"];

    function createCheckboxes() {
      const container = document.getElementById("checkboxes");
      container.innerHTML = "";
      symbols.forEach(symbol => {
        const groupId = `${symbol}_group`.replace(/ /g, "_");
        const groupLabel = document.createElement("label");
        groupLabel.className = "group-label";
        groupLabel.innerHTML = `<input type="checkbox" id="${groupId}" checked onchange="toggleGroup('${symbol}')"> ${symbol} (All Keys)`;
        container.appendChild(groupLabel);

        keys.forEach(key => {
          const id = `${symbol}_${key}`.replace(/ /g, "_");
          const label = document.createElement("label");
          label.innerHTML = `<input type="checkbox" id="${id}" ${key === 'lp' ? 'checked' : ''} onchange="renderChart()"> ${symbol} - ${key}`;
          container.appendChild(label);
        });
      });
    }

    function toggleGroup(symbol) {
      const groupId = `${symbol}_group`.replace(/ /g, "_");
      const checked = document.getElementById(groupId).checked;
      keys.forEach(key => {
        const id = `${symbol}_${key}`.replace(/ /g, "_");
        const box = document.getElementById(id);
        if (box) box.checked = checked;
      });
      renderChart();
    }

    async function fetchData(symbol, interval) {
      const res = await fetch(`http://127.0.0.1:8000/chart/${encodeURIComponent(symbol)}/${interval}`);
      return await res.json();
    }

    async function renderChart() {
      const interval = document.getElementById("interval").value;
      const layout = {
        title: "📊 Full Market Snapshot",
        xaxis: { title: "Time", type: "date" },
        showlegend: true
      };

      const traces = [];
      let axisCount = 1;

      for (let s = 0; s < symbols.length; s++) {
        const data = await fetchData(symbols[s], interval);
        for (let k = 0; k < keys.length; k++) {
          const id = `${symbols[s]}_${keys[k]}`.replace(/ /g, "_");
          const checkbox = document.getElementById(id);
          if (checkbox && checkbox.checked) {
            traces.push({
              x: data.map(p => p.time),
              y: data.map(p => p[keys[k]]),
              name: `${symbols[s]} - ${keys[k]}`,
              mode: "lines+markers",
              line: { color: colors[k % colors.length] },
              yaxis: `y${axisCount}`
            });
            layout[`yaxis${axisCount}`] = {
              title: `${symbols[s]} - ${keys[k]}`,
              overlaying: "y",
              side: axisCount % 2 === 0 ? "right" : "left",
              position: 0.05 + (axisCount * 0.02)
            };
            axisCount++;
          }
        }
      }

      Plotly.newPlot("chart", traces, layout);
    }

    createCheckboxes();
    renderChart();
    setInterval(renderChart, 5000);
  </script>
</body>
</html>