<!DOCTYPE html>
<html>
<head>
  <title>Full Market Chart Viewer</title>
  <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
  <style>
    body { font-family: Arial; margin: 20px; background: #fafafa; }
    .checkbox-group { margin-bottom: 20px; }
    .group-label {
      font-weight: bold;
      margin-top: 10px;
      display: flex;
      align-items: center;
      gap: 8px;
    }
    .group-label input[type="checkbox"] {
      transform: scale(1.2);
    }
    label {
      display: flex;
      align-items: center;
      gap: 6px;
      padding: 2px 0;
    }
    label:hover {
      background: #e8f4fd;
      border-radius: 3px;
      padding: 2px 4px;
    }
  </style>
</head>
<body>
  <h2>📈 Full Market Chart Viewer</h2>
  <label>Interval:
    <select id="interval">
      <option value="5">5s</option>
      <option value="30">30s</option>
      <option value="60">1m</option>
      <option value="300">5m</option>
      <option value="900">15m</option>
      <option value="4500">75m</option>
      <option value="86400">Daily</option>
    </select>
  </label>
  <button onclick="renderChart()">🔄 Refresh</button>

  <div class="checkbox-group" id="checkboxes"></div>
  <div id="chart" style="height: 700px;"></div>

  <script>
    const symbols = ["RELIANCE-EQ", "NIFTY INDEX", "INDIA VIX"];
    const keys = ["lp", "c", "h", "l", "o", "ap", "ltq", "tbq", "tsq", "tbuyq", "tsellq"];
    const colors = ["red", "blue", "green", "orange", "purple", "teal", "brown", "gray", "pink", "gold", "cyan"];

    function createCheckboxes() {
      const container = document.getElementById("checkboxes");
      container.innerHTML = "";

      symbols.forEach(symbol => {
        // Create symbol header
        const symbolHeader = document.createElement("div");
        symbolHeader.className = "group-label";
        symbolHeader.style.cssText = "background: #f0f0f0; padding: 8px; margin: 10px 0 5px 0; border-radius: 4px; font-weight: bold;";

        const groupId = `${symbol}_group`.replace(/ /g, "_");
        symbolHeader.innerHTML = `
          <input type="checkbox" id="${groupId}" checked onchange="toggleGroup('${symbol}')">
          📊 ${symbol}
        `;
        container.appendChild(symbolHeader);

        // Create keys container for this symbol
        const keysContainer = document.createElement("div");
        keysContainer.style.cssText = "display: grid; grid-template-columns: repeat(auto-fill, minmax(120px, 1fr)); gap: 5px; margin-left: 20px; margin-bottom: 15px; padding: 5px; border-left: 3px solid #ddd;";

        keys.forEach(key => {
          const id = `${symbol}_${key}`.replace(/ /g, "_");
          const label = document.createElement("label");
          label.style.cssText = "font-size: 14px; cursor: pointer;";
          label.innerHTML = `<input type="checkbox" id="${id}" ${key === 'lp' ? 'checked' : ''} onchange="renderChart()"> ${key.toUpperCase()}`;
          keysContainer.appendChild(label);
        });

        container.appendChild(keysContainer);
      });
    }

    function toggleGroup(symbol) {
      const groupId = `${symbol}_group`.replace(/ /g, "_");
      const checked = document.getElementById(groupId).checked;
      keys.forEach(key => {
        const id = `${symbol}_${key}`.replace(/ /g, "_");
        const box = document.getElementById(id);
        if (box) box.checked = checked;
      });
      renderChart();
    }

    async function fetchData(symbol, interval) {
      const res = await fetch(`http://127.0.0.1:8000/chart/${encodeURIComponent(symbol)}/${interval}`);
      return await res.json();
    }

    async function renderChart() {
      const interval = document.getElementById("interval").value;
      const layout = {
        title: "📊 Full Market Snapshot",
        xaxis: { title: "Time", type: "date" },
        showlegend: true,
        margin: { l: 80, r: 80, t: 50, b: 50 }
      };

      const traces = [];
      const symbolAxes = {}; // Track which axis each symbol uses
      let axisCount = 1;

      // Group selected items by symbol
      const selectedBySymbol = {};
      for (let s = 0; s < symbols.length; s++) {
        const symbol = symbols[s];
        selectedBySymbol[symbol] = [];

        for (let k = 0; k < keys.length; k++) {
          const id = `${symbol}_${keys[k]}`.replace(/ /g, "_");
          const checkbox = document.getElementById(id);
          if (checkbox && checkbox.checked) {
            selectedBySymbol[symbol].push(keys[k]);
          }
        }
      }

      // Create traces grouped by symbol
      for (const symbol of symbols) {
        if (selectedBySymbol[symbol].length === 0) continue;

        const data = await fetchData(symbol, interval);
        const yAxisKey = axisCount === 1 ? "y" : `y${axisCount}`;
        symbolAxes[symbol] = yAxisKey;

        // Create axis for this symbol
        if (axisCount === 1) {
          layout.yaxis = {
            title: symbol,
            side: "left",
            titlefont: { color: colors[(axisCount - 1) % colors.length] },
            tickfont: { color: colors[(axisCount - 1) % colors.length] }
          };
        } else {
          const side = axisCount % 2 === 0 ? "right" : "left";
          const position = side === "right" ?
            1 - (Math.floor((axisCount - 1) / 2) * 0.12) :
            Math.floor(axisCount / 2) * 0.12;

          layout[`yaxis${axisCount}`] = {
            title: symbol,
            titlefont: { color: colors[(axisCount - 1) % colors.length] },
            tickfont: { color: colors[(axisCount - 1) % colors.length] },
            overlaying: "y",
            side: side,
            position: Math.max(0, Math.min(1, position))
          };
        }

        // Add traces for each selected key of this symbol
        selectedBySymbol[symbol].forEach((key, keyIndex) => {
          traces.push({
            x: data.map(p => p.time),
            y: data.map(p => p[key]),
            name: key.toUpperCase(),
            mode: "lines+markers",
            line: {
              color: colors[(axisCount - 1) % colors.length],
              dash: keyIndex === 0 ? 'solid' : keyIndex === 1 ? 'dash' : keyIndex === 2 ? 'dot' : 'dashdot'
            },
            yaxis: yAxisKey,
            legendgroup: symbol,
            legendgrouptitle: { text: symbol }
          });
        });

        axisCount++;
      }

      Plotly.newPlot("chart", traces, layout);
    }

    createCheckboxes();
    renderChart();
    setInterval(renderChart, 5000);
  </script>
</body>
</html>