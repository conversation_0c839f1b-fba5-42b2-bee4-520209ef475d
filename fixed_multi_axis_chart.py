# fixed_multi_axis_chart.py - Complete Multi-Axis Market Chart with Working Controls
import warnings
warnings.simplefilter(action='ignore', category=FutureWarning)

import dash
from dash import dcc, html, Input, Output, State, ALL, callback_context
import plotly.graph_objs as go
import pandas as pd
import json
import psycopg2
from datetime import datetime, timedelta
from zoneinfo import ZoneInfo

# --- Database Configuration ---
DB_CONFIG = {
    "dbname": "eq_DB",
    "user": "postgres", 
    "password": "Muni@555",
    "host": "localhost",
    "port": "5432"
}

# --- Constants ---
IST = ZoneInfo("Asia/Kolkata")
SYMBOLS = ["RELIANCE-EQ", "NIFTY INDEX", "INDIA VIX"]
KEYS = ["lp", "c", "h", "l", "o", "ap", "ltq", "tbq", "tsq", "tbuyq", "tsellq"]

# Color scheme for different metrics
METRIC_COLORS = {
    'lp': '#FF4444',    # Red - Last Price
    'c': '#44FF44',     # Green - Close
    'h': '#4444FF',     # Blue - High
    'l': '#FFFF44',     # Yellow - Low
    'o': '#FF44FF',     # Magenta - Open
    'ap': '#44FFFF',    # Cyan - Average Price
    'ltq': '#FF8844',   # Orange - Last Trade Qty
    'tbq': '#8844FF',   # Purple - Total Buy Qty
    'tsq': '#44FF88',   # Light Green - Total Sell Qty
    'tbuyq': '#FF8888', # Light Red - Total Buy Qty
    'tsellq': '#8888FF' # Light Blue - Total Sell Qty
}

# --- Dash App ---
app = dash.Dash(__name__)
app.title = "📈 Multi-Axis Market Chart"

# --- Layout ---
app.layout = html.Div(style={
    'backgroundColor': '#0a0a0a', 
    'color': '#ffffff', 
    'fontFamily': 'Arial',
    'padding': '15px',
    'minHeight': '100vh'
}, children=[
    html.H1("📈 Complete Multi-Axis Market Chart", 
            style={'textAlign': 'center', 'color': '#00ff88', 'marginBottom': '20px'}),
    
    # Controls
    html.Div([
        html.Div([
            html.Label("📅 Select Date:", style={'fontWeight': 'bold', 'color': '#00ff88'}),
            dcc.DatePickerSingle(
                id='date-picker',
                date=datetime.now(IST).date(),
                display_format='DD-MM-YYYY',
                style={'marginLeft': '10px'}
            )
        ], style={'marginRight': '30px'}),
        
        html.Div([
            html.Label("📊 Chart Mode:", style={'fontWeight': 'bold', 'color': '#00ff88'}),
            dcc.RadioItems(
                id='chart-mode',
                options=[
                    {'label': ' 🌟 ALL DATA', 'value': 'multi'},
                    {'label': ' 💰 PRICE ONLY', 'value': 'price'},
                    {'label': ' 📈 VOLUME ONLY', 'value': 'volume'}
                ],
                value='multi',
                inline=True,
                style={'marginLeft': '10px', 'color': '#ffffff'}
            )
        ], style={'marginRight': '30px'}),
        
        html.Button("🔄 Refresh Data", id='refresh-btn', 
                   style={'backgroundColor': '#00ff88', 'color': '#000', 'border': 'none',
                          'padding': '12px 25px', 'borderRadius': '8px', 'cursor': 'pointer',
                          'fontWeight': 'bold', 'fontSize': '14px'})
    ], style={'display': 'flex', 'alignItems': 'center', 'justifyContent': 'center', 
              'marginBottom': '25px', 'flexWrap': 'wrap', 'gap': '15px'}),
    
    # Chart
    dcc.Graph(id='multi-axis-chart', style={'height': '700px'}),
    
    # Line Visibility Controls
    html.Div([
        html.H3("🎛️ Line Visibility Controls", style={'color': '#00ff88', 'textAlign': 'center', 'marginBottom': '15px'}),
        html.Div(id='line-controls-container', style={'maxHeight': '300px', 'overflowY': 'auto', 'padding': '15px',
                                                     'backgroundColor': '#1a1a1a', 'borderRadius': '8px', 'margin': '0 20px'})
    ], style={'marginTop': '20px', 'marginBottom': '20px'}),
    
    # Status
    html.Div([
        html.Div(id='chart-status', style={'textAlign': 'center', 'color': '#00ff88', 'fontSize': '16px'}),
        html.Div(id='legend-info', style={'textAlign': 'center', 'color': '#888', 'fontSize': '12px', 'marginTop': '10px'})
    ]),
    
    # Data store
    dcc.Store(id='chart-data-store'),
    
    # Auto-refresh
    dcc.Interval(id='auto-refresh', interval=30*1000, n_intervals=0)
])

def fetch_all_data(selected_date):
    """Fetch ALL data for all symbols and metrics"""
    try:
        print(f"🔍 Fetching ALL data for {selected_date}")
        
        conn = psycopg2.connect(**DB_CONFIG)
        cur = conn.cursor()
        
        start_time = datetime.combine(selected_date, datetime.min.time()).replace(tzinfo=IST)
        end_time = start_time + timedelta(days=1)
        
        cur.execute("""
            SELECT symbol, request_time, raw_json
            FROM quote_snapshots
            WHERE request_time >= %s AND request_time < %s
            ORDER BY symbol, request_time
        """, (start_time, end_time))
        
        rows = cur.fetchall()
        cur.close()
        conn.close()
        
        print(f"📊 Found {len(rows)} database records")
        
        if not rows:
            return pd.DataFrame()
        
        # Process ALL data
        data_list = []
        for symbol, request_time, raw_json in rows:
            try:
                if isinstance(raw_json, dict):
                    json_data = raw_json
                else:
                    json_data = json.loads(raw_json)
                
                record = {'symbol': symbol, 'timestamp': request_time}
                for key in KEYS:
                    try:
                        value = json_data.get(key, 0)
                        record[key] = float(value) if value else 0
                    except:
                        record[key] = 0
                
                data_list.append(record)
                
            except Exception as e:
                continue
        
        if not data_list:
            return pd.DataFrame()
        
        df = pd.DataFrame(data_list)
        print(f"✅ Processed {len(df)} records for {df['symbol'].nunique()} symbols")
        return df
        
    except Exception as e:
        print(f"❌ Database error: {e}")
        return pd.DataFrame()

@app.callback(
    [Output('line-controls-container', 'children'),
     Output('chart-data-store', 'data')],
    [Input('date-picker', 'date'),
     Input('chart-mode', 'value'),
     Input('refresh-btn', 'n_clicks'),
     Input('auto-refresh', 'n_intervals')]
)
def update_controls_and_data(selected_date_str, chart_mode, refresh_clicks, auto_refresh):
    try:
        selected_date = datetime.strptime(selected_date_str, '%Y-%m-%d').date()
        df = fetch_all_data(selected_date)
        
        if df.empty:
            return html.Div("No data available", style={'color': '#888', 'textAlign': 'center'}), {}
        
        # Filter keys based on mode
        if chart_mode == 'price':
            active_keys = ['lp', 'c', 'h', 'l', 'o', 'ap']
        elif chart_mode == 'volume':
            active_keys = ['ltq', 'tbq', 'tsq', 'tbuyq', 'tsellq']
        else:  # multi
            active_keys = KEYS
        
        # Create controls for each symbol
        controls = []
        for symbol in SYMBOLS:
            symbol_data = df[df['symbol'] == symbol]
            if symbol_data.empty:
                continue
            
            # Get available keys for this symbol (non-zero data)
            available_keys = []
            for key in active_keys:
                if key in symbol_data.columns and symbol_data[key].sum() != 0:
                    available_keys.append(key)
            
            if not available_keys:
                continue
            
            # Create symbol control section
            symbol_id = symbol.replace(' ', '-').replace(':', '')
            
            controls.append(
                html.Div([
                    html.Div([
                        html.H5(f"📊 {symbol}", style={
                            'color': '#00ff88',
                            'margin': '0 15px 0 0',
                            'display': 'inline-block'
                        }),
                        html.Button("Toggle All", 
                                   id={'type': 'toggle-btn', 'symbol': symbol_id},
                                   style={
                                       'backgroundColor': '#333',
                                       'color': '#fff',
                                       'border': '1px solid #555',
                                       'borderRadius': '4px',
                                       'padding': '4px 12px',
                                       'fontSize': '11px',
                                       'cursor': 'pointer'
                                   })
                    ], style={'marginBottom': '10px', 'display': 'flex', 'alignItems': 'center'}),
                    
                    dcc.Checklist(
                        id={'type': 'line-checkbox', 'symbol': symbol_id},
                        options=[{
                            'label': html.Span([
                                html.Span("●", style={'color': METRIC_COLORS.get(key, '#ffffff'), 'marginRight': '8px', 'fontSize': '16px'}),
                                html.Span(key.upper(), style={'fontSize': '13px'})
                            ]),
                            'value': f"{symbol}_{key}"
                        } for key in available_keys],
                        value=[f"{symbol}_{key}" for key in available_keys],  # All checked by default
                        inline=True,
                        style={'marginLeft': '20px', 'display': 'grid', 'gridTemplateColumns': 'repeat(auto-fit, minmax(80px, 1fr))', 'gap': '8px'}
                    )
                ], style={
                    'marginBottom': '20px',
                    'padding': '12px',
                    'backgroundColor': '#2a2a2a',
                    'borderRadius': '6px',
                    'borderLeft': f'4px solid #00ff88'
                })
            )
        
        return controls, df.to_json(orient='split', date_format='iso')
        
    except Exception as e:
        print(f"❌ Error updating controls: {e}")
        return html.Div(f"Error: {str(e)}", style={'color': '#ff4444'}), {}

@app.callback(
    Output({'type': 'line-checkbox', 'symbol': ALL}, 'value'),
    Input({'type': 'toggle-btn', 'symbol': ALL}, 'n_clicks'),
    State({'type': 'line-checkbox', 'symbol': ALL}, 'options'),
    prevent_initial_call=True
)
def toggle_symbol_lines(n_clicks_list, options_list):
    ctx = callback_context
    if not ctx.triggered:
        return dash.no_update
    
    # Find which button was clicked
    button_id = ctx.triggered[0]['prop_id'].split('.')[0]
    button_data = json.loads(button_id)
    clicked_symbol = button_data['symbol']
    
    # Find the corresponding options
    result = []
    for i, options in enumerate(options_list):
        if i < len(n_clicks_list) and n_clicks_list[i]:
            # This is the clicked symbol - toggle it
            if n_clicks_list[i] % 2 == 1:
                result.append([])  # Uncheck all
            else:
                result.append([opt['value'] for opt in options])  # Check all
        else:
            # Keep current state for other symbols
            result.append(dash.no_update)
    
    return result

@app.callback(
    [Output('multi-axis-chart', 'figure'),
     Output('chart-status', 'children'),
     Output('legend-info', 'children')],
    [Input({'type': 'line-checkbox', 'symbol': ALL}, 'value'),
     Input('chart-data-store', 'data'),
     Input('chart-mode', 'value')]
)
def create_chart(selected_lines_list, stored_data, chart_mode):
    try:
        if not stored_data:
            empty_fig = go.Figure()
            empty_fig.update_layout(
                title="📊 No data available",
                template='plotly_dark',
                height=700,
                paper_bgcolor='#0a0a0a',
                plot_bgcolor='#1a1a1a'
            )
            return empty_fig, "❌ No data", ""

        # Load data from store
        df = pd.read_json(stored_data, orient='split')
        df['timestamp'] = pd.to_datetime(df['timestamp'])

        # Collect all selected lines
        selected_lines = []
        for lines_list in selected_lines_list:
            if lines_list:
                selected_lines.extend(lines_list)

        if not selected_lines:
            empty_fig = go.Figure()
            empty_fig.update_layout(
                title="📊 No lines selected - Use controls below",
                template='plotly_dark',
                height=700,
                paper_bgcolor='#0a0a0a',
                plot_bgcolor='#1a1a1a'
            )
            return empty_fig, "⚠️ No lines selected", "Use the checkboxes below to select data to display"

        # Create figure
        fig = go.Figure()
        axis_count = 1
        total_traces = 0

        # Create traces for selected lines
        for line_id in selected_lines:
            try:
                symbol, key = line_id.split('_', 1)

                symbol_data = df[df['symbol'] == symbol].copy()
                if symbol_data.empty or key not in symbol_data.columns:
                    continue

                # Skip if all values are zero
                if symbol_data[key].sum() == 0:
                    continue

                symbol_data = symbol_data.sort_values('timestamp')

                # Assign y-axis
                yaxis_name = 'y' if axis_count == 1 else f'y{axis_count}'

                # Create trace
                fig.add_trace(go.Scatter(
                    x=symbol_data['timestamp'],
                    y=symbol_data[key],
                    mode='lines',
                    name=f'{symbol} - {key.upper()}',
                    line=dict(
                        color=METRIC_COLORS.get(key, '#ffffff'),
                        width=3 if key == 'lp' else 2
                    ),
                    yaxis=yaxis_name,
                    legendgroup=symbol,
                    legendgrouptitle=dict(text=f"📊 {symbol}")
                ))

                # Configure y-axis
                axis_title = f"{symbol} - {key.upper()}"
                axis_color = METRIC_COLORS.get(key, '#ffffff')

                if axis_count == 1:
                    fig.update_layout(
                        yaxis=dict(
                            title=dict(text=axis_title, font=dict(color=axis_color, size=10)),
                            side='left',
                            showgrid=True,
                            gridcolor='rgba(128,128,128,0.1)',
                            tickfont=dict(color=axis_color, size=9)
                        )
                    )
                else:
                    # Alternate sides and positions
                    side = 'right' if axis_count % 2 == 0 else 'left'
                    if side == 'right':
                        position = 1.0 - ((axis_count // 2 - 1) * 0.08)
                    else:
                        position = (axis_count // 2) * 0.08

                    position = max(0.02, min(0.98, position))

                    fig.update_layout(**{
                        f'yaxis{axis_count}': dict(
                            title=dict(text=axis_title, font=dict(color=axis_color, size=10)),
                            overlaying='y',
                            side=side,
                            position=position,
                            showgrid=False,
                            tickfont=dict(color=axis_color, size=9)
                        )
                    })

                axis_count += 1
                total_traces += 1

            except Exception as e:
                print(f"❌ Error processing line {line_id}: {e}")
                continue

        # Update layout
        mode_titles = {'multi': '🌟 ALL DATA', 'price': '💰 PRICE DATA', 'volume': '📈 VOLUME DATA'}
        mode_title = mode_titles.get(chart_mode, '📊 DATA')

        fig.update_layout(
            title=dict(
                text=f"📈 {mode_title} | {total_traces} Series on {axis_count-1} Independent Axes",
                font=dict(size=16, color='#00ff88'),
                x=0.5
            ),
            template='plotly_dark',
            height=700,
            paper_bgcolor='#0a0a0a',
            plot_bgcolor='#1a1a1a',
            xaxis=dict(
                title='Time (IST)',
                showgrid=True,
                gridcolor='rgba(128,128,128,0.1)',
                tickfont=dict(size=10)
            ),
            legend=dict(
                orientation='v',
                yanchor='top',
                y=1,
                xanchor='left',
                x=1.02,
                font=dict(size=9),
                bgcolor='rgba(0,0,0,0.5)'
            ),
            margin=dict(l=100, r=200, t=80, b=60)
        )

        # Status messages
        status_msg = f"✅ Displaying {total_traces} data series across {axis_count-1} independent axes"
        legend_msg = f"🎨 Each line has its own Y-axis scale | 🎛️ Use controls below to show/hide lines"

        return fig, status_msg, legend_msg

    except Exception as e:
        print(f"❌ Chart creation error: {e}")
        import traceback
        traceback.print_exc()

        error_fig = go.Figure()
        error_fig.update_layout(
            title=f"❌ Error: {str(e)}",
            template='plotly_dark',
            height=700,
            paper_bgcolor='#0a0a0a'
        )
        return error_fig, f"❌ Error: {str(e)}", ""

if __name__ == '__main__':
    print("🚀 Starting Fixed Multi-Axis Market Chart...")
    print("📊 This version has working hide/show controls")
    print("🌐 Open http://localhost:8053 in your browser")
    app.run_server(debug=True, host='0.0.0.0', port=8053)
