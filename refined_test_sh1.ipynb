{"cells": [{"cell_type": "code", "execution_count": 9, "id": "7888f934", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "    \"request_time\": \"12:05:46 08-10-2025\",\n", "    \"stat\": \"Ok\",\n", "    \"exch\": \"NSE\",\n", "    \"tsym\": \"Nifty 50\",\n", "    \"lut\": \"12:05:46 08-10-2025\",\n", "    \"token\": \"26000\",\n", "    \"lp\": \"25030.75\",\n", "    \"c\": \"25108.30\",\n", "    \"h\": \"25192.50\",\n", "    \"l\": \"25022.50\",\n", "    \"o\": \"25079.75\"\n", "}\n"]}], "source": ["import json\n", "from datetime import datetime\n", "from zoneinfo import ZoneInfo\n", "from NorenRestApiPy.NorenApi import NorenApi\n", "\n", "# 🔧 Hardcoded input\n", "EXCHANGE = \"NSE\"\n", "TRADING_SYMBOL = \"NIFTY INDEX\"\n", "\n", "REST_URL = \"https://api.shoonya.com/NorenWClientTP/\"\n", "WS_URL   = \"wss://api.shoonya.com/NorenWSTP/\"\n", "\n", "# ✅ Keys to extract from full quote\n", "FILTER_KEYS = [\n", "    \"request_time\", \"stat\", \"exch\", \"tsym\", \"lut\", \"token\", \"lp\", \"c\", \"h\", \"l\", \"ap\", \"o\", \"v\", \"ltq\", \"ltt\",\n", "    \"tbq\", \"tsq\",\n", "    \"bp1\", \"sp1\", \"bp2\", \"sp2\", \"bp3\", \"sp3\", \"bp4\", \"sp4\", \"bp5\", \"sp5\",\n", "    \"bq1\", \"sq1\", \"bq2\", \"sq2\", \"bq3\", \"sq3\", \"bq4\", \"sq4\", \"bq5\", \"sq5\",\n", "    \"bo1\", \"so1\", \"bo2\", \"so2\", \"bo3\", \"so3\", \"bo4\", \"so4\", \"bo5\", \"so5\"\n", "]\n", "\n", "class ShoonyaSessionLoader:\n", "    def __init__(self, cred_file=\"Cread.json\", token_file=\"session_token.txt\"):\n", "        self.cred_file, self.token_file = cred_file, token_file\n", "        self.api = None\n", "\n", "    def load(self):\n", "        try:\n", "            with open(self.cred_file) as cf, open(self.token_file) as tf:\n", "                creds, token = json.load(cf), tf.read().strip()\n", "            self.api = NorenApi(host=REST_URL, websocket=WS_URL)\n", "            self.api.set_session(creds[\"user_id\"], creds[\"password\"], token)\n", "            return True\n", "        except Exception as e:\n", "            print(f\"[ShoonyaSessionLoader] Error: {e}\")\n", "            return False\n", "\n", "def fetch_filtered_quote(exchange: str, symbol: str):\n", "    loader = ShoonyaSessionLoader()\n", "    if not loader.load(): return\n", "\n", "    api = loader.api\n", "    query = symbol.upper()\n", "    scrips = api.searchscrip(exchange=exchange, searchtext=query)\n", "    if not scrips or \"values\" not in scrips:\n", "        print(f\"[Search] No match found for {query}\")\n", "        return\n", "\n", "    token = scrips[\"values\"][0][\"token\"]\n", "    quote = api.get_quotes(exchange=exchange, token=token)\n", "    if not quote or quote.get(\"stat\") != \"Ok\":\n", "        print(f\"[Quote] Failed to fetch quote for token {token}\")\n", "        return\n", "\n", "    # ⏰ Add IST timestamp for request_time\n", "    ist_now = datetime.now(ZoneInfo(\"Asia/Kolkata\")).strftime(\"%H:%M:%S %d-%m-%Y\")\n", "    quote[\"request_time\"] = ist_now\n", "\n", "    # ⏰ Convert lut (epoch) to IST\n", "    try:\n", "        lut_epoch = int(quote.get(\"lut\", \"0\"))\n", "        quote[\"lut\"] = datetime.fromtimestamp(lut_epoch, ZoneInfo(\"Asia/Kolkata\")).strftime(\"%H:%M:%S %d-%m-%Y\")\n", "    except:\n", "        quote[\"lut\"] = \"Invalid timestamp\"\n", "\n", "    # 🧹 Filter output\n", "    filtered = {k: quote[k] for k in FILTER_KEYS if k in quote}\n", "    print(json.dumps(filtered, indent=4))\n", "\n", "# 🚀 Run with hardcoded values\n", "fetch_filtered_quote(EXCHAN<PERSON>, TRADING_SYMBOL)"]}, {"cell_type": "code", "execution_count": 10, "id": "145d0fa0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "    \"request_time\": \"12:06:24 08-10-2025\",\n", "    \"stat\": \"Ok\",\n", "    \"exch\": \"NSE\",\n", "    \"tsym\": \"INDIAVIX\",\n", "    \"lut\": \"12:06:08 08-10-2025\",\n", "    \"token\": \"26017\",\n", "    \"lp\": \"10.46\",\n", "    \"c\": \"10.05\",\n", "    \"h\": \"10.50\",\n", "    \"l\": \"9.69\",\n", "    \"o\": \"10.05\"\n", "}\n"]}], "source": ["import json\n", "from datetime import datetime\n", "from zoneinfo import ZoneInfo\n", "from NorenRestApiPy.NorenApi import NorenApi\n", "\n", "# 🔧 Hardcoded input\n", "EXCHANGE = \"NSE\"\n", "TRADING_SYMBOL = \"INDIA VIX\"\n", "\n", "REST_URL = \"https://api.shoonya.com/NorenWClientTP/\"\n", "WS_URL   = \"wss://api.shoonya.com/NorenWSTP/\"\n", "\n", "# ✅ Keys to extract from full quote\n", "FILTER_KEYS = [\n", "    \"request_time\", \"stat\", \"exch\", \"tsym\", \"lut\", \"token\", \"lp\", \"c\", \"h\", \"l\", \"ap\", \"o\", \"v\", \"ltq\", \"ltt\",\n", "    \"tbq\", \"tsq\",\n", "    \"bp1\", \"sp1\", \"bp2\", \"sp2\", \"bp3\", \"sp3\", \"bp4\", \"sp4\", \"bp5\", \"sp5\",\n", "    \"bq1\", \"sq1\", \"bq2\", \"sq2\", \"bq3\", \"sq3\", \"bq4\", \"sq4\", \"bq5\", \"sq5\",\n", "    \"bo1\", \"so1\", \"bo2\", \"so2\", \"bo3\", \"so3\", \"bo4\", \"so4\", \"bo5\", \"so5\"\n", "]\n", "\n", "class ShoonyaSessionLoader:\n", "    def __init__(self, cred_file=\"Cread.json\", token_file=\"session_token.txt\"):\n", "        self.cred_file, self.token_file = cred_file, token_file\n", "        self.api = None\n", "\n", "    def load(self):\n", "        try:\n", "            with open(self.cred_file) as cf, open(self.token_file) as tf:\n", "                creds, token = json.load(cf), tf.read().strip()\n", "            self.api = NorenApi(host=REST_URL, websocket=WS_URL)\n", "            self.api.set_session(creds[\"user_id\"], creds[\"password\"], token)\n", "            return True\n", "        except Exception as e:\n", "            print(f\"[ShoonyaSessionLoader] Error: {e}\")\n", "            return False\n", "\n", "def fetch_filtered_quote(exchange: str, symbol: str):\n", "    loader = ShoonyaSessionLoader()\n", "    if not loader.load(): return\n", "\n", "    api = loader.api\n", "    query = symbol.upper()\n", "    scrips = api.searchscrip(exchange=exchange, searchtext=query)\n", "    if not scrips or \"values\" not in scrips:\n", "        print(f\"[Search] No match found for {query}\")\n", "        return\n", "\n", "    token = scrips[\"values\"][0][\"token\"]\n", "    quote = api.get_quotes(exchange=exchange, token=token)\n", "    if not quote or quote.get(\"stat\") != \"Ok\":\n", "        print(f\"[Quote] Failed to fetch quote for token {token}\")\n", "        return\n", "\n", "    # ⏰ Add IST timestamp for request_time\n", "    ist_now = datetime.now(ZoneInfo(\"Asia/Kolkata\")).strftime(\"%H:%M:%S %d-%m-%Y\")\n", "    quote[\"request_time\"] = ist_now\n", "\n", "    # ⏰ Convert lut (epoch) to IST\n", "    try:\n", "        lut_epoch = int(quote.get(\"lut\", \"0\"))\n", "        quote[\"lut\"] = datetime.fromtimestamp(lut_epoch, ZoneInfo(\"Asia/Kolkata\")).strftime(\"%H:%M:%S %d-%m-%Y\")\n", "    except:\n", "        quote[\"lut\"] = \"Invalid timestamp\"\n", "\n", "    # 🧹 Filter output\n", "    filtered = {k: quote[k] for k in FILTER_KEYS if k in quote}\n", "    print(json.dumps(filtered, indent=4))\n", "\n", "# 🚀 Run with hardcoded values\n", "fetch_filtered_quote(EXCHAN<PERSON>, TRADING_SYMBOL)"]}, {"cell_type": "code", "execution_count": 11, "id": "2192fa81", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "    \"request_time\": \"12:08:10 08-10-2025\",\n", "    \"stat\": \"Ok\",\n", "    \"exch\": \"NSE\",\n", "    \"tsym\": \"RELIANCE-EQ\",\n", "    \"lut\": \"12:08:09 08-10-2025\",\n", "    \"token\": \"2885\",\n", "    \"lp\": \"1368.70\",\n", "    \"c\": \"1384.80\",\n", "    \"h\": \"1389.00\",\n", "    \"l\": \"1368.20\",\n", "    \"ap\": \"1376.74\",\n", "    \"o\": \"1384.80\",\n", "    \"v\": \"3138601\",\n", "    \"ltq\": \"2\",\n", "    \"ltt\": \"12:08:09\",\n", "    \"tbq\": \"991682\",\n", "    \"tsq\": \"640644\",\n", "    \"bp1\": \"1368.50\",\n", "    \"sp1\": \"1368.70\",\n", "    \"bp2\": \"1368.40\",\n", "    \"sp2\": \"1368.80\",\n", "    \"bp3\": \"1368.30\",\n", "    \"sp3\": \"1368.90\",\n", "    \"bp4\": \"1368.20\",\n", "    \"sp4\": \"1369.00\",\n", "    \"bp5\": \"1368.10\",\n", "    \"sp5\": \"1369.10\",\n", "    \"bq1\": \"68\",\n", "    \"sq1\": \"84\",\n", "    \"bq2\": \"764\",\n", "    \"sq2\": \"227\",\n", "    \"bq3\": \"1074\",\n", "    \"sq3\": \"478\",\n", "    \"bq4\": \"1836\",\n", "    \"sq4\": \"2325\",\n", "    \"bq5\": \"1086\",\n", "    \"sq5\": \"909\",\n", "    \"bo1\": \"6\",\n", "    \"so1\": \"6\",\n", "    \"bo2\": \"13\",\n", "    \"so2\": \"6\",\n", "    \"bo3\": \"28\",\n", "    \"so3\": \"8\",\n", "    \"bo4\": \"38\",\n", "    \"so4\": \"7\",\n", "    \"bo5\": \"31\",\n", "    \"so5\": \"4\"\n", "}\n"]}], "source": ["import json\n", "from datetime import datetime\n", "from zoneinfo import ZoneInfo\n", "from NorenRestApiPy.NorenApi import NorenApi\n", "\n", "# 🔧 Hardcoded input\n", "EXCHANGE = \"NSE\"\n", "TRADING_SYMBOL = \"RELIANCE-EQ\"\n", "\n", "REST_URL = \"https://api.shoonya.com/NorenWClientTP/\"\n", "WS_URL   = \"wss://api.shoonya.com/NorenWSTP/\"\n", "\n", "# ✅ Keys to extract from full quote\n", "FILTER_KEYS = [\n", "    \"request_time\", \"stat\", \"exch\", \"tsym\", \"lut\", \"token\", \"lp\", \"c\", \"h\", \"l\", \"ap\", \"o\", \"v\", \"ltq\", \"ltt\",\n", "    \"tbq\", \"tsq\",\n", "    \"bp1\", \"sp1\", \"bp2\", \"sp2\", \"bp3\", \"sp3\", \"bp4\", \"sp4\", \"bp5\", \"sp5\",\n", "    \"bq1\", \"sq1\", \"bq2\", \"sq2\", \"bq3\", \"sq3\", \"bq4\", \"sq4\", \"bq5\", \"sq5\",\n", "    \"bo1\", \"so1\", \"bo2\", \"so2\", \"bo3\", \"so3\", \"bo4\", \"so4\", \"bo5\", \"so5\"\n", "]\n", "\n", "class ShoonyaSessionLoader:\n", "    def __init__(self, cred_file=\"Cread.json\", token_file=\"session_token.txt\"):\n", "        self.cred_file, self.token_file = cred_file, token_file\n", "        self.api = None\n", "\n", "    def load(self):\n", "        try:\n", "            with open(self.cred_file) as cf, open(self.token_file) as tf:\n", "                creds, token = json.load(cf), tf.read().strip()\n", "            self.api = NorenApi(host=REST_URL, websocket=WS_URL)\n", "            self.api.set_session(creds[\"user_id\"], creds[\"password\"], token)\n", "            return True\n", "        except Exception as e:\n", "            print(f\"[ShoonyaSessionLoader] Error: {e}\")\n", "            return False\n", "\n", "def fetch_filtered_quote(exchange: str, symbol: str):\n", "    loader = ShoonyaSessionLoader()\n", "    if not loader.load(): return\n", "\n", "    api = loader.api\n", "    query = symbol.upper()\n", "    scrips = api.searchscrip(exchange=exchange, searchtext=query)\n", "    if not scrips or \"values\" not in scrips:\n", "        print(f\"[Search] No match found for {query}\")\n", "        return\n", "\n", "    token = scrips[\"values\"][0][\"token\"]\n", "    quote = api.get_quotes(exchange=exchange, token=token)\n", "    if not quote or quote.get(\"stat\") != \"Ok\":\n", "        print(f\"[Quote] Failed to fetch quote for token {token}\")\n", "        return\n", "\n", "    # ⏰ Add IST timestamp for request_time\n", "    ist_now = datetime.now(ZoneInfo(\"Asia/Kolkata\")).strftime(\"%H:%M:%S %d-%m-%Y\")\n", "    quote[\"request_time\"] = ist_now\n", "\n", "    # ⏰ Convert lut (epoch) to IST\n", "    try:\n", "        lut_epoch = int(quote.get(\"lut\", \"0\"))\n", "        quote[\"lut\"] = datetime.fromtimestamp(lut_epoch, ZoneInfo(\"Asia/Kolkata\")).strftime(\"%H:%M:%S %d-%m-%Y\")\n", "    except:\n", "        quote[\"lut\"] = \"Invalid timestamp\"\n", "\n", "    # 🧹 Filter output\n", "    filtered = {k: quote[k] for k in FILTER_KEYS if k in quote}\n", "    print(json.dumps(filtered, indent=4))\n", "\n", "# 🚀 Run with hardcoded values\n", "fetch_filtered_quote(EXCHAN<PERSON>, TRADING_SYMBOL)"]}, {"cell_type": "code", "execution_count": 12, "id": "e1467db0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "    \"RELIANCE-EQ\": {\n", "        \"request_time\": \"12:16:54 08-10-2025\",\n", "        \"stat\": \"Ok\",\n", "        \"exch\": \"NSE\",\n", "        \"tsym\": \"RELIANCE-EQ\",\n", "        \"lut\": \"12:16:53 08-10-2025\",\n", "        \"token\": \"2885\",\n", "        \"lp\": \"1369.10\",\n", "        \"c\": \"1384.80\",\n", "        \"h\": \"1389.00\",\n", "        \"l\": \"1368.20\",\n", "        \"ap\": \"1376.58\",\n", "        \"o\": \"1384.80\",\n", "        \"v\": \"3204104\",\n", "        \"ltq\": \"11\",\n", "        \"ltt\": \"12:16:50\",\n", "        \"tbq\": \"1011263\",\n", "        \"tsq\": \"657551\",\n", "        \"bp1\": \"1369.10\",\n", "        \"sp1\": \"1369.20\",\n", "        \"bp2\": \"1369.00\",\n", "        \"sp2\": \"1369.30\",\n", "        \"bp3\": \"1368.90\",\n", "        \"sp3\": \"1369.40\",\n", "        \"bp4\": \"1368.80\",\n", "        \"sp4\": \"1369.50\",\n", "        \"bp5\": \"1368.70\",\n", "        \"sp5\": \"1369.60\",\n", "        \"bq1\": \"355\",\n", "        \"sq1\": \"1208\",\n", "        \"bq2\": \"3032\",\n", "        \"sq2\": \"2825\",\n", "        \"bq3\": \"143\",\n", "        \"sq3\": \"3030\",\n", "        \"bq4\": \"198\",\n", "        \"sq4\": \"2026\",\n", "        \"bq5\": \"561\",\n", "        \"sq5\": \"9548\",\n", "        \"bo1\": \"14\",\n", "        \"so1\": \"7\",\n", "        \"bo2\": \"45\",\n", "        \"so2\": \"15\",\n", "        \"bo3\": \"4\",\n", "        \"so3\": \"16\",\n", "        \"bo4\": \"11\",\n", "        \"so4\": \"15\",\n", "        \"bo5\": \"18\",\n", "        \"so5\": \"21\"\n", "    },\n", "    \"NIFTY INDEX\": {\n", "        \"request_time\": \"12:16:55 08-10-2025\",\n", "        \"stat\": \"Ok\",\n", "        \"exch\": \"NSE\",\n", "        \"tsym\": \"Nifty 50\",\n", "        \"lut\": \"12:16:55 08-10-2025\",\n", "        \"token\": \"26000\",\n", "        \"lp\": \"25041.55\",\n", "        \"c\": \"25108.30\",\n", "        \"h\": \"25192.50\",\n", "        \"l\": \"25022.50\",\n", "        \"o\": \"25079.75\"\n", "    },\n", "    \"INDIA VIX\": {\n", "        \"request_time\": \"12:16:56 08-10-2025\",\n", "        \"stat\": \"Ok\",\n", "        \"exch\": \"NSE\",\n", "        \"tsym\": \"INDIAVIX\",\n", "        \"lut\": \"12:16:55 08-10-2025\",\n", "        \"token\": \"26017\",\n", "        \"lp\": \"10.42\",\n", "        \"c\": \"10.05\",\n", "        \"h\": \"10.50\",\n", "        \"l\": \"9.69\",\n", "        \"o\": \"10.05\"\n", "    }\n", "}\n"]}], "source": ["import json\n", "from datetime import datetime\n", "from zoneinfo import ZoneInfo\n", "from NorenRestApiPy.NorenApi import NorenApi\n", "\n", "# 🔧 One hardcoded input — your target symbol\n", "TARGET_SYMBOL = \"RELIANCE-EQ\"\n", "EXCHANGE = \"NSE\"\n", "\n", "REST_URL = \"https://api.shoonya.com/NorenWClientTP/\"\n", "WS_URL   = \"wss://api.shoonya.com/NorenWSTP/\"\n", "\n", "FILTER_KEYS = [\n", "    \"request_time\", \"stat\", \"exch\", \"tsym\", \"lut\", \"token\", \"lp\", \"c\", \"h\", \"l\", \"ap\", \"o\", \"v\", \"ltq\", \"ltt\",\n", "    \"tbq\", \"tsq\", \"bp1\", \"sp1\", \"bp2\", \"sp2\", \"bp3\", \"sp3\", \"bp4\", \"sp4\", \"bp5\", \"sp5\",\n", "    \"bq1\", \"sq1\", \"bq2\", \"sq2\", \"bq3\", \"sq3\", \"bq4\", \"sq4\", \"bq5\", \"sq5\",\n", "    \"bo1\", \"so1\", \"bo2\", \"so2\", \"bo3\", \"so3\", \"bo4\", \"so4\", \"bo5\", \"so5\"\n", "]\n", "\n", "class ShoonyaSessionLoader:\n", "    def __init__(self, cred_file=\"Cread.json\", token_file=\"session_token.txt\"):\n", "        self.cred_file, self.token_file = cred_file, token_file\n", "        self.api = None\n", "\n", "    def load(self):\n", "        try:\n", "            with open(self.cred_file) as cf, open(self.token_file) as tf:\n", "                creds, token = json.load(cf), tf.read().strip()\n", "            self.api = NorenApi(host=REST_URL, websocket=WS_URL)\n", "            self.api.set_session(creds[\"user_id\"], creds[\"password\"], token)\n", "            return self.api\n", "        except Exception as e:\n", "            print(f\"[ShoonyaSessionLoader] Error: {e}\")\n", "            return None\n", "\n", "def get_quote(api, symbol):\n", "    try:\n", "        query = symbol.upper()\n", "        scrips = api.searchscrip(exchange=EXCHANGE, searchtext=query)\n", "        if not scrips or \"values\" not in scrips: return {}\n", "\n", "        token = scrips[\"values\"][0][\"token\"]\n", "        quote = api.get_quotes(exchange=EXCHANGE, token=token)\n", "        if not quote or quote.get(\"stat\") != \"Ok\": return {}\n", "\n", "        ist_now = datetime.now(ZoneInfo(\"Asia/Kolkata\")).strftime(\"%H:%M:%S %d-%m-%Y\")\n", "        quote[\"request_time\"] = ist_now\n", "\n", "        try:\n", "            lut_epoch = int(quote.get(\"lut\", \"0\"))\n", "            quote[\"lut\"] = datetime.fromtimestamp(lut_epoch, ZoneInfo(\"Asia/Kolkata\")).strftime(\"%H:%M:%S %d-%m-%Y\")\n", "        except:\n", "            quote[\"lut\"] = \"Invalid timestamp\"\n", "\n", "        return {k: quote[k] for k in FILTER_KEYS if k in quote}\n", "    except:\n", "        return {}\n", "\n", "def kick(symbol):\n", "    loader = ShoonyaSessionLoader()\n", "    api = loader.load()\n", "    if not api: return\n", "\n", "    symbols = [symbol, \"NIFTY INDEX\", \"INDIA VIX\"]\n", "    combined = {s.upper(): get_quote(api, s) for s in symbols}\n", "    print(json.dumps(combined, indent=4))\n", "\n", "# 🚀 One kick — unified output\n", "kick(TARGET_SYMBOL)"]}, {"cell_type": "code", "execution_count": 14, "id": "4b899b7a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "    \"RELIANCE-EQ\": {\n", "        \"request_time\": \"12:34:11 08-10-2025\",\n", "        \"stat\": \"Ok\",\n", "        \"exch\": \"NSE\",\n", "        \"tsym\": \"RELIANCE-EQ\",\n", "        \"lut\": \"12:34:11 08-10-2025\",\n", "        \"token\": \"2885\",\n", "        \"lp\": \"1365.60\",\n", "        \"c\": \"1384.80\",\n", "        \"h\": \"1389.00\",\n", "        \"l\": \"1364.50\",\n", "        \"ap\": \"1375.14\",\n", "        \"o\": \"1384.80\",\n", "        \"v\": \"3735795\",\n", "        \"ltq\": \"18\",\n", "        \"ltt\": \"12:34:09\",\n", "        \"tbq\": \"968362\",\n", "        \"tsq\": \"677947\",\n", "        \"bp1\": \"1365.80\",\n", "        \"sp1\": \"1366.10\",\n", "        \"bp2\": \"1365.60\",\n", "        \"sp2\": \"1366.20\",\n", "        \"bp3\": \"1365.50\",\n", "        \"sp3\": \"1366.30\",\n", "        \"bp4\": \"1365.40\",\n", "        \"sp4\": \"1366.40\",\n", "        \"bp5\": \"1365.30\",\n", "        \"sp5\": \"1366.50\",\n", "        \"bq1\": \"7\",\n", "        \"sq1\": \"3367\",\n", "        \"bq2\": \"89\",\n", "        \"sq2\": \"4905\",\n", "        \"bq3\": \"2199\",\n", "        \"sq3\": \"3943\",\n", "        \"bq4\": \"982\",\n", "        \"sq4\": \"5098\",\n", "        \"bq5\": \"714\",\n", "        \"sq5\": \"1618\",\n", "        \"bo1\": \"3\",\n", "        \"so1\": \"14\",\n", "        \"bo2\": \"1\",\n", "        \"so2\": \"18\",\n", "        \"bo3\": \"22\",\n", "        \"so3\": \"15\",\n", "        \"bo4\": \"10\",\n", "        \"so4\": \"21\",\n", "        \"bo5\": \"8\",\n", "        \"so5\": \"13\",\n", "        \"tbuyq\": \"3991\",\n", "        \"tsellq\": \"18931\"\n", "    },\n", "    \"NIFTY INDEX\": {\n", "        \"request_time\": \"12:34:12 08-10-2025\",\n", "        \"stat\": \"Ok\",\n", "        \"exch\": \"NSE\",\n", "        \"tsym\": \"Nifty 50\",\n", "        \"lut\": \"12:34:11 08-10-2025\",\n", "        \"token\": \"26000\",\n", "        \"lp\": \"25018.40\",\n", "        \"c\": \"25108.30\",\n", "        \"h\": \"25192.50\",\n", "        \"l\": \"25008.50\",\n", "        \"o\": \"25079.75\"\n", "    },\n", "    \"INDIA VIX\": {\n", "        \"request_time\": \"12:34:13 08-10-2025\",\n", "        \"stat\": \"Ok\",\n", "        \"exch\": \"NSE\",\n", "        \"tsym\": \"INDIAVIX\",\n", "        \"lut\": \"12:34:08 08-10-2025\",\n", "        \"token\": \"26017\",\n", "        \"lp\": \"10.48\",\n", "        \"c\": \"10.05\",\n", "        \"h\": \"10.52\",\n", "        \"l\": \"9.69\",\n", "        \"o\": \"10.05\"\n", "    }\n", "}\n"]}], "source": ["import json\n", "from datetime import datetime\n", "from zoneinfo import ZoneInfo\n", "from NorenRestApiPy.NorenApi import NorenApi\n", "\n", "# 🔧 One hardcoded input\n", "TARGET_SYMBOL = \"RELIANCE-EQ\"\n", "EXCHANGE = \"NSE\"\n", "\n", "REST_URL = \"https://api.shoonya.com/NorenWClientTP/\"\n", "WS_URL   = \"wss://api.shoonya.com/NorenWSTP/\"\n", "\n", "FILTER_KEYS = [\n", "    \"request_time\", \"stat\", \"exch\", \"tsym\", \"lut\", \"token\", \"lp\", \"c\", \"h\", \"l\", \"ap\", \"o\", \"v\", \"ltq\", \"ltt\",\n", "    \"tbq\", \"tsq\", \"bp1\", \"sp1\", \"bp2\", \"sp2\", \"bp3\", \"sp3\", \"bp4\", \"sp4\", \"bp5\", \"sp5\",\n", "    \"bq1\", \"sq1\", \"bq2\", \"sq2\", \"bq3\", \"sq3\", \"bq4\", \"sq4\", \"bq5\", \"sq5\",\n", "    \"bo1\", \"so1\", \"bo2\", \"so2\", \"bo3\", \"so3\", \"bo4\", \"so4\", \"bo5\", \"so5\"\n", "]\n", "\n", "class ShoonyaSessionLoader:\n", "    def __init__(self, cred_file=\"Cread.json\", token_file=\"session_token.txt\"):\n", "        self.cred_file, self.token_file = cred_file, token_file\n", "        self.api = None\n", "\n", "    def load(self):\n", "        try:\n", "            with open(self.cred_file) as cf, open(self.token_file) as tf:\n", "                creds, token = json.load(cf), tf.read().strip()\n", "            self.api = NorenApi(host=REST_URL, websocket=WS_URL)\n", "            self.api.set_session(creds[\"user_id\"], creds[\"password\"], token)\n", "            return self.api\n", "        except Exception as e:\n", "            print(f\"[ShoonyaSessionLoader] Error: {e}\")\n", "            return None\n", "\n", "def get_quote(api, symbol):\n", "    try:\n", "        query = symbol.upper()\n", "        scrips = api.searchscrip(exchange=EXCHANGE, searchtext=query)\n", "        if not scrips or \"values\" not in scrips: return {}\n", "\n", "        token = scrips[\"values\"][0][\"token\"]\n", "        quote = api.get_quotes(exchange=EXCHANGE, token=token)\n", "        if not quote or quote.get(\"stat\") != \"Ok\": return {}\n", "\n", "        ist_now = datetime.now(ZoneInfo(\"Asia/Kolkata\")).strftime(\"%H:%M:%S %d-%m-%Y\")\n", "        quote[\"request_time\"] = ist_now\n", "\n", "        try:\n", "            lut_epoch = int(quote.get(\"lut\", \"0\"))\n", "            quote[\"lut\"] = datetime.fromtimestamp(lut_epoch, ZoneInfo(\"Asia/Kolkata\")).strftime(\"%H:%M:%S %d-%m-%Y\")\n", "        except:\n", "            quote[\"lut\"] = \"Invalid timestamp\"\n", "\n", "        filtered = {k: quote[k] for k in FILTER_KEYS if k in quote}\n", "\n", "        # 🧮 Sum bid/ask quantities if symbol ends with EQ\n", "        if symbol.upper().endswith(\"EQ\"):\n", "            tbuyq = sum(int(filtered.get(f\"bq{i}\", 0)) for i in range(1, 6))\n", "            tsellq = sum(int(filtered.get(f\"sq{i}\", 0)) for i in range(1, 6))\n", "            filtered[\"tbuyq\"] = str(tbuyq)\n", "            filtered[\"tsellq\"] = str(tsellq)\n", "\n", "        return filtered\n", "    except:\n", "        return {}\n", "\n", "def kick(symbol):\n", "    loader = ShoonyaSessionLoader()\n", "    api = loader.load()\n", "    if not api: return\n", "\n", "    symbols = [symbol, \"NIFTY INDEX\", \"INDIA VIX\"]\n", "    combined = {s.upper(): get_quote(api, s) for s in symbols}\n", "    print(json.dumps(combined, indent=4))\n", "\n", "# 🚀 One kick — unified output with tbuyq and tsellq for EQ\n", "kick(TARGET_SYMBOL)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 5}