# eq_std_to_db_sh1.py
import json
import psycopg2
import msvcrt
import threading
import sys
from time import sleep
from datetime import datetime, time, timedelta
from zoneinfo import ZoneInfo
from NorenRestApiPy.NorenApi import NorenApi

# 🔧 Shoonya credentials
CRED_FILE = "Cread.json"
TOKEN_FILE = "session_token.txt"

# 🔧 PostgreSQL credentials
DB_NAME = "eq_DB"
DB_USER = "postgres"
DB_PASS = "Muni@555"
DB_HOST = "localhost"
DB_PORT = "5432"

# 🔧 Kick symbols
SYMBOLS = ["RELIANCE-EQ", "NIFTY INDEX", "INDIA VIX"]
EXCHANGE = "NSE"

FILTER_KEYS = [
    "request_time", "stat", "exch", "tsym", "lut", "token", "lp", "c", "h", "l", "ap", "o", "v", "ltq", "ltt",
    "tbq", "tsq", "bp1", "sp1", "bp2", "sp2", "bp3", "sp3", "bp4", "sp4", "bp5", "sp5",
    "bq1", "sq1", "bq2", "sq2", "bq3", "sq3", "bq4", "sq4", "bq5", "sq5",
    "bo1", "so1", "bo2", "so2", "bo3", "so3", "bo4", "so4", "bo5", "so5"
]

def wait_for_market_open():
    now = datetime.now(ZoneInfo("Asia/Kolkata"))
    target = datetime.combine(now.date(), time(8, 59, 50)).replace(tzinfo=ZoneInfo("Asia/Kolkata"))
    if now < target:
        wait_sec = int((target - now).total_seconds())
        print(f"⏳ Waiting until 08:59:50 IST to trigger first record… ({wait_sec}s)")
        try:
            for i in range(wait_sec):
                if msvcrt.kbhit():
                    key = msvcrt.getch()
                    print(f"⛔ Key pressed: {key.decode('utf-8', errors='ignore')}. Exiting early.")
                    return False
                sleep(1)
        except KeyboardInterrupt:
            print("⛔ Ctrl + C detected. Exiting gracefully.")
            return False
    return True

def is_market_open():
    now = datetime.now(ZoneInfo("Asia/Kolkata"))
    t = now.time()
    d = now.date()

    if time(9, 0) <= t <= time(15, 30):
        return True

    if d == datetime(2025, 10, 21).date() and time(13, 15) <= t <= time(15, 30):
        print("🪔 Diwali Muhurat Trading Session detected.")
        choice = input("Do you want to proceed with data fetch? (y/n): ").strip().lower()
        return choice == "y"

    print("⛔ Market closed. Try during market hours or special session.")
    return False

def load_shoonya_session():
    with open(CRED_FILE) as cf, open(TOKEN_FILE) as tf:
        creds, token = json.load(cf), tf.read().strip()
    api = NorenApi(host="https://api.shoonya.com/NorenWClientTP/", websocket="wss://api.shoonya.com/NorenWSTP/")
    api.set_session(creds["user_id"], creds["password"], token)
    print(f"✅ Shoonya session loaded for user: {creds['user_id']}")
    return api
def fetch_symbol(api, symbol, request_time, output):
    try:
        scrips = api.searchscrip(exchange=EXCHANGE, searchtext=symbol.upper())
        if not scrips or "values" not in scrips: return
        token = scrips["values"][0]["token"]
        quote = api.get_quotes(exchange=EXCHANGE, token=token)
        if not quote or quote.get("stat") != "Ok": return

        quote["request_time"] = request_time
        try:
            lut_epoch = int(quote.get("lut", "0"))
            quote["lut"] = datetime.fromtimestamp(lut_epoch, ZoneInfo("Asia/Kolkata")).strftime("%H:%M:%S %d-%m-%Y")
        except:
            quote["lut"] = "Invalid timestamp"

        filtered = {k: quote[k] for k in FILTER_KEYS if k in quote}
        if symbol.upper().endswith("EQ"):
            filtered["tbuyq"] = str(sum(int(filtered.get(f"bq{i}", 0)) for i in range(1, 6)))
            filtered["tsellq"] = str(sum(int(filtered.get(f"sq{i}", 0)) for i in range(1, 6)))
        output[symbol.upper()] = filtered
    except:
        pass

def print_summary_line(symbol, total, historical, current, days, min_time, max_time):
    line = (
        f"{symbol:<12} | Total: {total:<5} | Hist: {historical:<5} | Today: {current:<5} | Days: {days:<2} | "
        f"First: {min_time.strftime('%H:%M:%S')} | Last: {max_time.strftime('%H:%M:%S')}"
    )
    print(line)

def insert_into_db(data):
    conn = psycopg2.connect(dbname=DB_NAME, user=DB_USER, password=DB_PASS, host=DB_HOST, port=DB_PORT)
    cur = conn.cursor()
    for symbol, snapshot in data.items():
        request_time = datetime.strptime(snapshot["request_time"], "%H:%M:%S %d-%m-%Y")
        cur.execute("""
            INSERT INTO quote_snapshots (symbol, request_time, raw_json)
            VALUES (%s, %s, %s)
        """, (symbol, request_time, json.dumps(snapshot)))
        print(f"📥 {symbol} inserted at {snapshot['request_time']}")

    conn.commit()

    # Move cursor up to overwrite previous summary
    lines_to_clear = len(data)
    sys.stdout.write(f"\033[{lines_to_clear * 2}A")
    sys.stdout.write("\033[J")

    for symbol in data:
        cur.execute("SELECT COUNT(*) FROM quote_snapshots WHERE symbol = %s", (symbol,))
        total = cur.fetchone()[0]

        cur.execute("""
            SELECT COUNT(*) FROM quote_snapshots
            WHERE symbol = %s AND request_time::date < CURRENT_DATE
        """, (symbol,))
        historical = cur.fetchone()[0]

        cur.execute("""
            SELECT COUNT(*) FROM quote_snapshots
            WHERE symbol = %s AND request_time::date = CURRENT_DATE
        """, (symbol,))
        current_day = cur.fetchone()[0]

        cur.execute("""
            SELECT COUNT(DISTINCT request_time::date)
            FROM quote_snapshots WHERE symbol = %s
        """, (symbol,))
        days = cur.fetchone()[0]

        cur.execute("""
            SELECT MIN(request_time), MAX(request_time)
            FROM quote_snapshots WHERE symbol = %s
        """, (symbol,))
        min_time, max_time = cur.fetchone()

        print_summary_line(symbol, total, historical, current_day, days, min_time, max_time)

    cur.close()
    conn.close()
def get_next_interval(interval_sec=5):
    now = datetime.now(ZoneInfo("Asia/Kolkata"))
    next_tick = now.replace(microsecond=0) + timedelta(seconds=interval_sec)
    remainder = next_tick.second % interval_sec
    if remainder != 0:
        next_tick += timedelta(seconds=(interval_sec - remainder))
    return next_tick

def run_continuous(interval_sec=5):
    now = datetime.now(ZoneInfo("Asia/Kolkata"))
    if time(8, 30) <= now.time() < time(9, 0):
        if not wait_for_market_open(): return
    if not is_market_open(): return

    api = load_shoonya_session()
    print(f"🚀 Starting precision kick every {interval_sec}s until market close…")
    try:
        while True:
            now = datetime.now(ZoneInfo("Asia/Kolkata")).time()
            if now > time(15, 30):
                print("🛑 Market closed. Stopping kick.")
                break

            next_tick = get_next_interval(interval_sec)
            wait_time = (next_tick - datetime.now(ZoneInfo("Asia/Kolkata"))).total_seconds()
            sleep(max(0, wait_time))

            request_time = next_tick.strftime("%H:%M:%S %d-%m-%Y")
            combined = {}
            threads = []
            for symbol in SYMBOLS:
                t = threading.Thread(target=fetch_symbol, args=(api, symbol, request_time, combined))
                threads.append(t)
                t.start()
            for t in threads:
                t.join()
            insert_into_db(combined)

            if msvcrt.kbhit():
                key = msvcrt.getch()
                print(f"⛔ Key pressed: {key.decode('utf-8', errors='ignore')}. Exiting loop.")
                break
    except KeyboardInterrupt:
        print("🛑 Ctrl + C detected. Exiting loop.")

# 🚀 Run the kick loop
run_continuous(interval_sec=5)
