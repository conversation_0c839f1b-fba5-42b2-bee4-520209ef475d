# simple_market_chart.py - Simplified Market Chart using Dash
import warnings
warnings.simplefilter(action='ignore', category=FutureWarning)

import dash
from dash import dcc, html, Input, Output, State
import plotly.graph_objs as go
import pandas as pd
import json
import psycopg2
from datetime import datetime, timedelta
from zoneinfo import ZoneInfo

# --- Database Configuration ---
DB_CONFIG = {
    "dbname": "eq_DB",
    "user": "postgres", 
    "password": "Muni@555",
    "host": "localhost",
    "port": "5432"
}

# --- Constants ---
IST = ZoneInfo("Asia/Kolkata")
SYMBOLS = ["RELIANCE-EQ", "NIFTY INDEX", "INDIA VIX"]
KEYS = ["lp", "c", "h", "l", "o", "ap", "ltq", "tbq", "tsq", "tbuyq", "tsellq"]
COLORS = ["#FF6B6B", "#4ECDC4", "#45B7D1", "#96CEB4", "#FFEAA7", "#DDA0DD", "#98D8C8", "#F7DC6F", "#BB8FCE"]

# --- Dash App ---
app = dash.Dash(__name__)
app.title = "📈 Market Chart"

# --- Layout ---
app.layout = html.Div(style={
    'backgroundColor': '#1e1e1e', 
    'color': '#ffffff', 
    'fontFamily': 'Arial',
    'padding': '20px',
    'minHeight': '100vh'
}, children=[
    html.H1("📈 Market Chart Viewer", style={'textAlign': 'center', 'color': '#4ECDC4'}),
    
    # Controls
    html.Div([
        html.Div([
            html.Label("Date:", style={'fontWeight': 'bold'}),
            dcc.DatePickerSingle(
                id='date-picker',
                date=datetime.now(IST).date(),
                display_format='DD-MM-YYYY'
            )
        ], style={'marginRight': '20px'}),
        
        html.Button("🔄 Refresh", id='refresh-btn', 
                   style={'backgroundColor': '#4ECDC4', 'color': 'white', 'border': 'none',
                          'padding': '10px 20px', 'borderRadius': '5px', 'cursor': 'pointer'})
    ], style={'display': 'flex', 'alignItems': 'center', 'justifyContent': 'center', 'marginBottom': '20px'}),
    
    # Symbol Selection
    html.Div([
        html.H3("Select Data to Display:", style={'color': '#4ECDC4'}),
        
        # RELIANCE-EQ
        html.Div([
            html.H4("📊 RELIANCE-EQ", style={'color': '#FF6B6B', 'marginBottom': '10px'}),
            dcc.Checklist(
                id='reliance-keys',
                options=[{'label': f' {key.upper()}', 'value': key} for key in KEYS],
                value=['lp'],
                inline=True,
                style={'marginLeft': '20px'}
            )
        ], style={'marginBottom': '20px', 'padding': '10px', 'backgroundColor': '#2a2a2a', 'borderRadius': '5px'}),
        
        # NIFTY INDEX
        html.Div([
            html.H4("📊 NIFTY INDEX", style={'color': '#4ECDC4', 'marginBottom': '10px'}),
            dcc.Checklist(
                id='nifty-keys',
                options=[{'label': f' {key.upper()}', 'value': key} for key in KEYS],
                value=[],
                inline=True,
                style={'marginLeft': '20px'}
            )
        ], style={'marginBottom': '20px', 'padding': '10px', 'backgroundColor': '#2a2a2a', 'borderRadius': '5px'}),
        
        # INDIA VIX
        html.Div([
            html.H4("📊 INDIA VIX", style={'color': '#45B7D1', 'marginBottom': '10px'}),
            dcc.Checklist(
                id='vix-keys',
                options=[{'label': f' {key.upper()}', 'value': key} for key in KEYS],
                value=[],
                inline=True,
                style={'marginLeft': '20px'}
            )
        ], style={'marginBottom': '20px', 'padding': '10px', 'backgroundColor': '#2a2a2a', 'borderRadius': '5px'})
    ], style={'marginBottom': '20px'}),
    
    # Chart
    dcc.Graph(id='market-chart', style={'height': '700px'}),
    
    # Status
    html.Div(id='status', style={'textAlign': 'center', 'marginTop': '10px', 'color': '#888'})
])

def fetch_data(selected_date):
    """Fetch data from database"""
    try:
        print(f"🔍 Fetching data for {selected_date}")
        
        # Connect to database
        conn = psycopg2.connect(**DB_CONFIG)
        cur = conn.cursor()
        
        # Convert date to datetime range
        start_time = datetime.combine(selected_date, datetime.min.time()).replace(tzinfo=IST)
        end_time = start_time + timedelta(days=1)
        
        # Query data
        cur.execute("""
            SELECT symbol, request_time, raw_json
            FROM quote_snapshots
            WHERE request_time >= %s AND request_time < %s
            ORDER BY symbol, request_time
        """, (start_time, end_time))
        
        rows = cur.fetchall()
        cur.close()
        conn.close()
        
        print(f"📊 Found {len(rows)} database records")
        
        if not rows:
            return pd.DataFrame()
        
        # Process data
        data_list = []
        for symbol, request_time, raw_json in rows:
            try:
                # Parse JSON
                if isinstance(raw_json, dict):
                    json_data = raw_json
                else:
                    json_data = json.loads(raw_json)
                
                # Create record
                record = {'symbol': symbol, 'timestamp': request_time}
                for key in KEYS:
                    try:
                        value = json_data.get(key, 0)
                        record[key] = float(value) if value else 0
                    except:
                        record[key] = 0
                
                data_list.append(record)
                
            except Exception as e:
                print(f"❌ Error parsing record for {symbol}: {e}")
                continue
        
        if not data_list:
            return pd.DataFrame()
        
        df = pd.DataFrame(data_list)
        print(f"✅ Processed {len(df)} records successfully")
        return df
        
    except Exception as e:
        print(f"❌ Database error: {e}")
        return pd.DataFrame()

@app.callback(
    [Output('market-chart', 'figure'),
     Output('status', 'children')],
    [Input('refresh-btn', 'n_clicks'),
     Input('date-picker', 'date'),
     Input('reliance-keys', 'value'),
     Input('nifty-keys', 'value'),
     Input('vix-keys', 'value')]
)
def update_chart(n_clicks, selected_date_str, reliance_keys, nifty_keys, vix_keys):
    try:
        print(f"🔄 Chart update triggered")
        print(f"📅 Date: {selected_date_str}")
        print(f"📊 Selections - RELIANCE: {reliance_keys}, NIFTY: {nifty_keys}, VIX: {vix_keys}")
        
        # Parse date
        selected_date = datetime.strptime(selected_date_str, '%Y-%m-%d').date()
        
        # Fetch data
        df = fetch_data(selected_date)
        
        if df.empty:
            empty_fig = go.Figure()
            empty_fig.update_layout(
                title=f"📊 No data available for {selected_date.strftime('%d-%m-%Y')}",
                template='plotly_dark',
                height=700
            )
            return empty_fig, f"❌ No data found for {selected_date.strftime('%d-%m-%Y')}"
        
        # Create figure
        fig = go.Figure()
        axis_count = 1
        
        # Process each symbol
        symbol_configs = [
            ('RELIANCE-EQ', reliance_keys or [], '#FF6B6B'),
            ('NIFTY INDEX', nifty_keys or [], '#4ECDC4'), 
            ('INDIA VIX', vix_keys or [], '#45B7D1')
        ]
        
        for symbol, selected_keys, color in symbol_configs:
            if not selected_keys:
                continue
                
            symbol_data = df[df['symbol'] == symbol].copy()
            if symbol_data.empty:
                continue
            
            symbol_data = symbol_data.sort_values('timestamp')
            yaxis_name = 'y' if axis_count == 1 else f'y{axis_count}'
            
            # Add traces for each selected key
            for i, key in enumerate(selected_keys):
                if key in symbol_data.columns:
                    line_style = 'solid' if i == 0 else 'dash' if i == 1 else 'dot'
                    
                    fig.add_trace(go.Scatter(
                        x=symbol_data['timestamp'],
                        y=symbol_data[key],
                        mode='lines',
                        name=f'{symbol} - {key.upper()}',
                        line=dict(color=color, dash=line_style, width=2),
                        yaxis=yaxis_name
                    ))
            
            # Configure y-axis
            if axis_count == 1:
                fig.update_layout(yaxis=dict(title=symbol, titlefont=dict(color=color), side='left'))
            else:
                side = 'right' if axis_count % 2 == 0 else 'left'
                position = 1.0 - (axis_count // 2) * 0.1 if side == 'right' else (axis_count // 2) * 0.1
                
                fig.update_layout(**{
                    f'yaxis{axis_count}': dict(
                        title=symbol,
                        titlefont=dict(color=color),
                        overlaying='y',
                        side=side,
                        position=max(0, min(1, position))
                    )
                })
            
            axis_count += 1
        
        # Update layout
        fig.update_layout(
            title=f"📊 Market Data - {selected_date.strftime('%d-%m-%Y')}",
            template='plotly_dark',
            height=700,
            xaxis=dict(title='Time (IST)'),
            legend=dict(orientation='h', yanchor='bottom', y=1.02, xanchor='right', x=1),
            margin=dict(l=80, r=80, t=100, b=50)
        )
        
        total_traces = len(fig.data)
        status_msg = f"✅ Displaying {total_traces} data series for {selected_date.strftime('%d-%m-%Y')}"
        
        return fig, status_msg
        
    except Exception as e:
        print(f"❌ Chart update error: {e}")
        import traceback
        traceback.print_exc()
        
        error_fig = go.Figure()
        error_fig.update_layout(
            title=f"❌ Error: {str(e)}",
            template='plotly_dark',
            height=700
        )
        return error_fig, f"❌ Error: {str(e)}"

if __name__ == '__main__':
    print("🚀 Starting Simple Market Chart...")
    print("📊 Open http://localhost:8051 in your browser")
    app.run_server(debug=True, host='0.0.0.0', port=8051)
