import json
import threading
from datetime import datetime
from zoneinfo import ZoneInfo
from NorenRestApiPy.NorenApi import NorenApi

# 🔧 One hardcoded input — your target symbol
TARGET_SYMBOL = "RELIANCE-EQ"
EXCHANGE = "NSE"

REST_URL = "https://api.shoonya.com/NorenWClientTP/"
WS_URL   = "wss://api.shoonya.com/NorenWSTP/"

FILTER_KEYS = [
    "request_time", "stat", "exch", "tsym", "lut", "token", "lp", "c", "h", "l", "ap", "o", "v", "ltq", "ltt",
    "tbq", "tsq", "bp1", "sp1", "bp2", "sp2", "bp3", "sp3", "bp4", "sp4", "bp5", "sp5",
    "bq1", "sq1", "bq2", "sq2", "bq3", "sq3", "bq4", "sq4", "bq5", "sq5",
    "bo1", "so1", "bo2", "so2", "bo3", "so3", "bo4", "so4", "bo5", "so5"
]

class ShoonyaSessionLoader:
    def __init__(self, cred_file="Cread.json", token_file="session_token.txt"):
        self.cred_file, self.token_file = cred_file, token_file
        self.api = None

    def load(self):
        try:
            with open(self.cred_file) as cf, open(self.token_file) as tf:
                creds, token = json.load(cf), tf.read().strip()
            self.api = NorenApi(host=REST_URL, websocket=WS_URL)
            self.api.set_session(creds["user_id"], creds["password"], token)
            return self.api
        except Exception as e:
            print(f"[ShoonyaSessionLoader] Error: {e}")
            return None

def fetch_symbol(api, symbol, request_time, output):
    try:
        scrips = api.searchscrip(exchange=EXCHANGE, searchtext=symbol.upper())
        if not scrips or "values" not in scrips: return

        token = scrips["values"][0]["token"]
        quote = api.get_quotes(exchange=EXCHANGE, token=token)
        if not quote or quote.get("stat") != "Ok": return

        quote["request_time"] = request_time
        try:
            lut_epoch = int(quote.get("lut", "0"))
            quote["lut"] = datetime.fromtimestamp(lut_epoch, ZoneInfo("Asia/Kolkata")).strftime("%H:%M:%S %d-%m-%Y")
        except:
            quote["lut"] = "Invalid timestamp"

        filtered = {k: quote[k] for k in FILTER_KEYS if k in quote}
        if symbol.upper().endswith("EQ"):
            filtered["tbuyq"] = str(sum(int(filtered.get(f"bq{i}", 0)) for i in range(1, 6)))
            filtered["tsellq"] = str(sum(int(filtered.get(f"sq{i}", 0)) for i in range(1, 6)))
        output[symbol.upper()] = filtered
    except:
        pass

def kick(symbol):
    loader = ShoonyaSessionLoader()
    api = loader.load()
    if not api: return

    symbols = [symbol, "NIFTY INDEX", "INDIA VIX"]
    request_time = datetime.now(ZoneInfo("Asia/Kolkata")).strftime("%H:%M:%S %d-%m-%Y")
    output = {}
    threads = []

    for s in symbols:
        t = threading.Thread(target=fetch_symbol, args=(api, s, request_time, output))
        threads.append(t)
        t.start()

    for t in threads:
        t.join()

    print(json.dumps(output, indent=4))

# 🚀 One kick — unified output with synchronized timestamp
kick(TARGET_SYMBOL)