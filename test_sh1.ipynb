{"cells": [{"cell_type": "code", "execution_count": 8, "id": "50ad81a8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[ShoonyaSessionLoader] Session loaded.\n"]}], "source": ["\"\"\"\n", "Minimal, fast loader for Sho<PERSON>a session with NorenApi instance.\n", "\"\"\"\n", "\n", "import json\n", "from NorenRestApiPy.NorenApi import NorenApi\n", "\n", "REST_URL = \"https://api.shoonya.com/NorenWClientTP/\"\n", "WS_URL   = \"wss://api.shoonya.com/NorenWSTP/\"\n", "\n", "class ShoonyaSessionLoader:\n", "    def __init__(self, cred_file=\"Cread.json\", token_file=\"session_token.txt\"):\n", "        self.cred_file, self.token_file = cred_file, token_file\n", "        self.api = None\n", "\n", "    def load(self):\n", "        try:\n", "            with open(self.cred_file) as cf, open(self.token_file) as tf:\n", "                creds, token = json.load(cf), tf.read().strip()\n", "            self.api = NorenApi(host=REST_URL, websocket=WS_URL)\n", "            self.api.set_session(creds[\"user_id\"], creds[\"password\"], token)\n", "            print(\"[ShoonyaSessionLoader] Session loaded.\")\n", "            return True\n", "        except Exception as e:\n", "            print(f\"[ShoonyaSessionLoader] Error: {e}\")\n", "            return False\n", "\n", "# Example usage\n", "if __name__ == \"__main__\":\n", "    loader = ShoonyaSessionLoader()\n", "    if loader.load():\n", "        api = loader.api\n", "        # Use api e.g. api.get_order_book()\n"]}, {"cell_type": "code", "execution_count": 13, "id": "c28a5cdf", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["RELIANCE-EQ token is 2885\n"]}], "source": ["exch  = 'NSE'\n", "query = 'RELIANCE-EQ' # multiple criteria to narrow results \n", "ret = api.searchscrip(exchange=exch, searchtext=query)\n", "\n", "if ret != None:\n", "    symbols = ret['values']\n", "    for symbol in symbols:\n", "        print('{0} token is {1}'.format(symbol['tsym'], symbol['token']))\n", "\n"]}, {"cell_type": "code", "execution_count": 14, "id": "f4ca2046", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'request_time': '11:44:36 08-10-2025',\n", " 'stat': 'Ok',\n", " 'exch': 'NSE',\n", " 'tsym': 'RELIANCE-EQ',\n", " 'cname': '<PERSON><PERSON><PERSON><PERSON><PERSON> INDUSTRIES LTD',\n", " 'symname': 'RELIANCE',\n", " 'seg': 'EQT',\n", " 'instname': 'EQ',\n", " 'isin': 'INE002A01018',\n", " 'pp': '2',\n", " 'ls': '1',\n", " 'ti': '0.10',\n", " 'mult': '1',\n", " 'lut': '1759904076',\n", " 'uc': '1523.20',\n", " 'lc': '1246.40',\n", " 'wk52_h': '1551.00',\n", " 'wk52_l': '1114.84',\n", " 'toi': '221418000',\n", " 'issuecap': '13532472634.000000',\n", " 'cutof_all': 'false',\n", " 'prcftr_d': '(1 / 1 ) * (1 / 1)',\n", " 'token': '2885',\n", " 'lp': '1370.20',\n", " 'c': '1384.80',\n", " 'h': '1389.00',\n", " 'l': '1368.20',\n", " 'ap': '1378.30',\n", " 'o': '1384.80',\n", " 'v': '2476355',\n", " 'ltq': '50',\n", " 'ltt': '11:44:36',\n", " 'ltd': '08-10-2025',\n", " 'tbq': '967087',\n", " 'tsq': '634555',\n", " 'bp1': '1370.00',\n", " 'sp1': '1370.20',\n", " 'bp2': '1369.90',\n", " 'sp2': '1370.30',\n", " 'bp3': '1369.80',\n", " 'sp3': '1370.40',\n", " 'bp4': '1369.70',\n", " 'sp4': '1370.50',\n", " 'bp5': '1369.60',\n", " 'sp5': '1370.60',\n", " 'bq1': '3074',\n", " 'sq1': '22',\n", " 'bq2': '36',\n", " 'sq2': '519',\n", " 'bq3': '64',\n", " 'sq3': '1371',\n", " 'bq4': '179',\n", " 'sq4': '553',\n", " 'bq5': '579',\n", " 'sq5': '2685',\n", " 'bo1': '26',\n", " 'so1': '1',\n", " 'bo2': '1',\n", " 'so2': '7',\n", " 'bo3': '3',\n", " 'so3': '12',\n", " 'bo4': '8',\n", " 'so4': '7',\n", " 'bo5': '6',\n", " 'so5': '18'}"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["exch  = 'NSE'\n", "token = '2885'\n", "ret = api.get_quotes(exchange=exch, token=token)\n", "ret"]}, {"cell_type": "code", "execution_count": null, "id": "80bf13c4", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 16, "id": "147a360d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[ShoonyaSessionLoader] Session loaded successfully.\n", "\n", "[✓] Cached lookup for RELIANCE-EQ: Token=2885, Exchange=NSE\n", "[ShoonyaSessionLoader] Session loaded successfully.\n", "\n", "Processed Quote Data:\n", "{\n", "    \"tsym\": \"RELIANCE-EQ\",\n", "    \"request_time\": \"11:45:06 08-10-2025\",\n", "    \"o\": \"1384.80\",\n", "    \"h\": \"1389.00\",\n", "    \"l\": \"1368.20\",\n", "    \"prev.close\": \"1384.80\",\n", "    \"ltp\": \"1370.10\",\n", "    \"ltq\": \"2\",\n", "    \"ltt\": \"11:45:05\",\n", "    \"v\": \"2481806\",\n", "    \"ap\": \"1378.28\",\n", "    \"toi\": \"221418000\",\n", "    \"oi\": \"0\",\n", "    \"totalbuyqty\": \"964322\",\n", "    \"totalsellqty\": \"637228\",\n", "    \"top5_total_bid_qty\": \"1171\",\n", "    \"top5_total_ask_qty\": \"11841\"\n", "}\n"]}], "source": ["from __future__ import annotations\n", "import json\n", "import time\n", "from typing import List, Dict\n", "from NorenRestApiPy.NorenApi import NorenApi\n", "\n", "# ----------------------------- #\n", "# S<PERSON>onya Session Loader\n", "# ----------------------------- #\n", "class ShoonyaSessionLoader:\n", "    REST_URL = \"https://api.shoonya.com/NorenWClientTP/\"\n", "    WS_URL   = \"wss://api.shoonya.com/NorenWSTP/\"\n", "\n", "    def __init__(self, cred_file: str = \"Cread.json\", token_file: str = \"session_token.txt\"):\n", "        self.cred_file  = cred_file\n", "        self.token_file = token_file\n", "        self._api       = None\n", "        self._creds     = None\n", "\n", "    @property\n", "    def api(self) -> <PERSON><PERSON><PERSON><PERSON>:\n", "        return self._api\n", "\n", "    def load(self) -> bool:\n", "        try:\n", "            with open(self.cred_file, \"r\") as fp:\n", "                self._creds = json.load(fp)\n", "            with open(self.token_file, \"r\") as fp:\n", "                token = fp.read().strip()\n", "        except Exception as e:\n", "            print(f\"[ShoonyaSessionLoader] Error loading files: {e}\")\n", "            return False\n", "\n", "        class _Api(NorenApi):\n", "            def __init__(self_):\n", "                super().__init__(host=ShoonyaSessionLoader.REST_URL, websocket=ShoonyaSessionLoader.WS_URL)\n", "\n", "        self._api = _Api()\n", "        self._api.set_session(self._creds[\"user_id\"], self._creds[\"password\"], token)\n", "        print(\"[ShoonyaSessionLoader] Session loaded successfully.\")\n", "        return True\n", "\n", "# ----------------------------- #\n", "# Quote <PERSON> with <PERSON>ll\n", "# ----------------------------- #\n", "def format_quote(token: str, quote: dict) -> None:\n", "    def get(field: str) -> str:\n", "        return quote.get(field, \"0\")\n", "\n", "    tsym = get(\"tsym\")\n", "    print(f\"\\n[✓] Cached lookup for {tsym}: Token={token}, Exchange={get('exch')}\")\n", "    print(\"[ShoonyaSessionLoader] Session loaded successfully.\\n\")\n", "\n", "    processed = {\n", "        \"tsym\": tsym,\n", "        \"request_time\": get(\"request_time\"),\n", "        \"o\": get(\"o\"),\n", "        \"h\": get(\"h\"),\n", "        \"l\": get(\"l\"),\n", "        \"prev.close\": get(\"c\"),\n", "        \"ltp\": get(\"lp\"),\n", "        \"ltq\": get(\"ltq\"),\n", "        \"ltt\": get(\"ltt\"),\n", "        \"v\": get(\"v\"),\n", "        \"ap\": get(\"ap\"),\n", "        \"toi\": get(\"toi\"),\n", "        \"oi\": get(\"oi\"),\n", "        \"totalbuyqty\": get(\"tbq\"),\n", "        \"totalsellqty\": get(\"tsq\"),\n", "        \"top5_total_bid_qty\": str(sum(int(get(f\"bq{i}\")) for i in range(1, 6))),\n", "        \"top5_total_ask_qty\": str(sum(int(get(f\"sq{i}\")) for i in range(1, 6)))\n", "    }\n", "\n", "    print(\"Processed Quote Data:\")\n", "    print(json.dumps(processed, indent=4))\n", "\n", "# ----------------------------- #\n", "# Fast Batch <PERSON>tcher\n", "# ----------------------------- #\n", "def fetch_quotes(api: NorenApi, tokens: List[str], exchange: str = \"NSE\", delay_ms: int = 200) -> None:\n", "    for token in tokens:\n", "        try:\n", "            quote = api.get_quotes(exchange=exchange, token=token)\n", "            format_quote(token, quote)\n", "        except Exception as e:\n", "            print(f\"[ERROR] Token {token} failed: {e}\")\n", "        time.sleep(delay_ms / 1000.0)  # Respect Shoonya rate limit\n", "\n", "# ----------------------------- #\n", "# Main Execution\n", "# ----------------------------- #\n", "if __name__ == \"__main__\":\n", "    loader = ShoonyaSessionLoader()\n", "    if loader.load():\n", "        api = loader.api\n", "        tokens = ['2885']  # Add more tokens as needed\n", "        fetch_quotes(api, tokens)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 5}