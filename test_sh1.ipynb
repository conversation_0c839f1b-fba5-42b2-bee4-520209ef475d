"""
Minimal, fast loader for Shoonya session with NorenApi instance.
"""

import json
from NorenRestApiPy.NorenApi import NorenApi

REST_URL = "https://api.shoonya.com/NorenWClientTP/"
WS_URL   = "wss://api.shoonya.com/NorenWSTP/"

class ShoonyaSessionLoader:
    def __init__(self, cred_file="Cread.json", token_file="session_token.txt"):
        self.cred_file, self.token_file = cred_file, token_file
        self.api = None

    def load(self):
        try:
            with open(self.cred_file) as cf, open(self.token_file) as tf:
                creds, token = json.load(cf), tf.read().strip()
            self.api = NorenApi(host=REST_URL, websocket=WS_URL)
            self.api.set_session(creds["user_id"], creds["password"], token)
            print("[ShoonyaSessionLoader] Session loaded.")
            return True
        except Exception as e:
            print(f"[ShoonyaSessionLoader] Error: {e}")
            return False

# Example usage
if __name__ == "__main__":
    loader = ShoonyaSessionLoader()
    if loader.load():
        api = loader.api
        # Use api e.g. api.get_order_book()


exch  = 'NSE'
query = 'RELIANCE-EQ' # multiple criteria to narrow results 
ret = api.searchscrip(exchange=exch, searchtext=query)

if ret != None:
    symbols = ret['values']
    for symbol in symbols:
        print('{0} token is {1}'.format(symbol['tsym'], symbol['token']))



exch  = 'NSE'
token = '2885'
ret = api.get_quotes(exchange=exch, token=token)
ret



from __future__ import annotations
import json
import time
from typing import List, Dict
from NorenRestApiPy.NorenApi import NorenApi

# ----------------------------- #
# Shoonya Session Loader
# ----------------------------- #
class ShoonyaSessionLoader:
    REST_URL = "https://api.shoonya.com/NorenWClientTP/"
    WS_URL   = "wss://api.shoonya.com/NorenWSTP/"

    def __init__(self, cred_file: str = "Cread.json", token_file: str = "session_token.txt"):
        self.cred_file  = cred_file
        self.token_file = token_file
        self._api       = None
        self._creds     = None

    @property
    def api(self) -> NorenApi:
        return self._api

    def load(self) -> bool:
        try:
            with open(self.cred_file, "r") as fp:
                self._creds = json.load(fp)
            with open(self.token_file, "r") as fp:
                token = fp.read().strip()
        except Exception as e:
            print(f"[ShoonyaSessionLoader] Error loading files: {e}")
            return False

        class _Api(NorenApi):
            def __init__(self_):
                super().__init__(host=ShoonyaSessionLoader.REST_URL, websocket=ShoonyaSessionLoader.WS_URL)

        self._api = _Api()
        self._api.set_session(self._creds["user_id"], self._creds["password"], token)
        print("[ShoonyaSessionLoader] Session loaded successfully.")
        return True

# ----------------------------- #
# Quote Formatter with Zero Fill
# ----------------------------- #
def format_quote(token: str, quote: dict) -> None:
    def get(field: str) -> str:
        return quote.get(field, "0")

    tsym = get("tsym")
    print(f"\n[✓] Cached lookup for {tsym}: Token={token}, Exchange={get('exch')}")
    print("[ShoonyaSessionLoader] Session loaded successfully.\n")

    processed = {
        "tsym": tsym,
        "request_time": get("request_time"),
        "o": get("o"),
        "h": get("h"),
        "l": get("l"),
        "prev.close": get("c"),
        "ltp": get("lp"),
        "ltq": get("ltq"),
        "ltt": get("ltt"),
        "v": get("v"),
        "ap": get("ap"),
        "toi": get("toi"),
        "oi": get("oi"),
        "totalbuyqty": get("tbq"),
        "totalsellqty": get("tsq"),
        "top5_total_bid_qty": str(sum(int(get(f"bq{i}")) for i in range(1, 6))),
        "top5_total_ask_qty": str(sum(int(get(f"sq{i}")) for i in range(1, 6)))
    }

    print("Processed Quote Data:")
    print(json.dumps(processed, indent=4))

# ----------------------------- #
# Fast Batch Fetcher
# ----------------------------- #
def fetch_quotes(api: NorenApi, tokens: List[str], exchange: str = "NSE", delay_ms: int = 200) -> None:
    for token in tokens:
        try:
            quote = api.get_quotes(exchange=exchange, token=token)
            format_quote(token, quote)
        except Exception as e:
            print(f"[ERROR] Token {token} failed: {e}")
        time.sleep(delay_ms / 1000.0)  # Respect Shoonya rate limit

# ----------------------------- #
# Main Execution
# ----------------------------- #
if __name__ == "__main__":
    loader = ShoonyaSessionLoader()
    if loader.load():
        api = loader.api
        tokens = ['2885']  # Add more tokens as needed
        fetch_quotes(api, tokens)